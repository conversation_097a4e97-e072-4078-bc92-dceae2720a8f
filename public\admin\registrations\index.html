<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON>n <PERSON> - VT Academy</title>
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="../../assets/images/favicon.png">
    <link rel="shortcut icon" type="image/png" href="../../assets/images/favicon.png">
    <link rel="apple-touch-icon" href="../../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="72x72" href="../../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="114x114" href="../../assets/images/favicon.png">

    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .registrations-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #ecf0f1;
        }

        .page-header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 2rem;
            font-weight: 700;
        }

        .back-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .stats-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .stat-card h3 {
            margin: 0 0 0.5rem 0;
            font-size: 1.8rem;
            font-weight: 700;
        }

        .stat-card p {
            margin: 0;
            color: #7f8c8d;
            font-weight: 500;
        }

        .stat-card:nth-child(1) h3 { color: #f39c12; }
        .stat-card:nth-child(2) h3 { color: #27ae60; }
        .stat-card:nth-child(3) h3 { color: #e74c3c; }
        .stat-card:nth-child(4) h3 { color: #3498db; }

        .filter-controls {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .filter-select {
            padding: 0.75rem 1rem;
            border: 2px solid #ecf0f1;
            border-radius: 10px;
            font-size: 1rem;
            background: white;
            min-width: 150px;
        }

        .registrations-table-container {
            overflow-x: auto;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .registrations-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            min-width: 1000px;
        }

        .registrations-table th,
        .registrations-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }

        .registrations-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .student-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .student-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        .student-details h4 {
            margin: 0 0 0.25rem 0;
            color: #2c3e50;
            font-weight: 600;
        }

        .student-details p {
            margin: 0;
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-approved {
            background: #d4edda;
            color: #155724;
        }

        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .action-btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 5px;
            font-size: 0.85rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .approve-btn {
            background: #27ae60;
            color: white;
        }

        .approve-btn:hover {
            background: #229954;
        }

        .reject-btn {
            background: #e74c3c;
            color: white;
        }

        .reject-btn:hover {
            background: #c0392b;
        }

        .view-btn {
            background: #3498db;
            color: white;
        }

        .view-btn:hover {
            background: #2980b9;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #7f8c8d;
        }

        .no-registrations {
            text-align: center;
            padding: 3rem;
            color: #7f8c8d;
        }

        .no-registrations i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #bdc3c7;
        }

        @media (max-width: 768px) {
            .stats-summary {
                grid-template-columns: 1fr 1fr;
            }
            
            .filter-controls {
                flex-direction: column;
            }
            
            .registrations-table th,
            .registrations-table td {
                padding: 0.5rem;
                font-size: 0.9rem;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../../index.html">Trang Chủ</a></li>
                    <li><a href="../../classes/">Lớp Học</a></li>
                    <li><a href="../../achievements/">Thành Tích</a></li>
                    <li><a href="../../auth/register.html">Đăng Ký</a></li>
                    <li><a href="../../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../../research/">Sự kiện</a></li>
                    <li><a href="../../auth/">Tài Khoản</a></li>
                    <li><a href="../" class="active">Quản Trị</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="admin-main">
        <div class="container">
            <div class="registrations-container">
                <div class="page-header">
                    <h1><i class="fas fa-file-alt"></i> Quản Lý Đơn Đăng Ký</h1>
                    <a href="../" class="back-btn">
                        <i class="fas fa-arrow-left"></i> Quay lại
                    </a>
                </div>

                <!-- Stats Summary -->
                <div class="stats-summary">
                    <div class="stat-card">
                        <h3 id="pendingCount">0</h3>
                        <p>Chờ duyệt</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="approvedCount">0</h3>
                        <p>Đã duyệt</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="rejectedCount">0</h3>
                        <p>Từ chối</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="totalCount">0</h3>
                        <p>Tổng đơn</p>
                    </div>
                </div>

                <!-- Filter Controls -->
                <div class="filter-controls">
                    <select id="statusFilter" class="filter-select">
                        <option value="">Tất cả trạng thái</option>
                        <option value="pending">Chờ duyệt</option>
                        <option value="approved">Đã duyệt</option>
                        <option value="rejected">Từ chối</option>
                    </select>
                    <select id="classFilter" class="filter-select">
                        <option value="">Tất cả khóa học</option>
                    </select>
                    <select id="dateFilter" class="filter-select">
                        <option value="">Tất cả thời gian</option>
                        <option value="today">Hôm nay</option>
                        <option value="week">Tuần này</option>
                        <option value="month">Tháng này</option>
                    </select>
                </div>

                <!-- Registrations Table -->
                <div class="registrations-table-container">
                    <div id="registrationsTableContainer">
                        <div class="loading">
                            <i class="fas fa-spinner fa-spin"></i>
                            <p>Đang tải danh sách đơn đăng ký...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../../assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";
        import {
            getFirestore,
            doc,
            getDoc,
            updateDoc,
            collection,
            query,
            getDocs,
            where,
            orderBy
        } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        let registrationsData = [];
        let classesData = [];

        // Check admin access
        onAuthStateChanged(auth, async (user) => {
            if (!user || user.email !== '<EMAIL>') {
                window.location.href = '../../auth/';
                return;
            }

            await loadRegistrations();
        });

        // Load registrations data
        async function loadRegistrations() {
            try {
                // Load classes first
                await loadClasses();

                // Get all course registrations (consultation requests)
                const registrationsQuery = query(collection(db, "course_registrations"), orderBy("registrationDate", "desc"));
                const registrationsSnapshot = await getDocs(registrationsQuery);

                registrationsData = [];
                registrationsSnapshot.forEach((doc) => {
                    const regData = doc.data();
                    registrationsData.push({
                        id: doc.id,
                        ...regData,
                        status: regData.status || 'pending'
                    });
                });

                displayRegistrations();
                updateStats();
                setupEventListeners();

            } catch (error) {
                console.error('Error loading registrations:', error);
                showError('Lỗi khi tải danh sách đơn đăng ký');
            }
        }

        // Load classes data
        async function loadClasses() {
            try {
                // Populate course filter with predefined courses
                const classFilter = document.getElementById('classFilter');
                classFilter.innerHTML = '<option value="">Tất cả khóa học</option>';

                const courses = [
                    { id: 'python', name: 'Python - AI từ Cơ Bản đến Nâng Cao' },
                    { id: 'scratch', name: 'Scratch - Tin Học Cơ Bản' },
                    { id: 'stem', name: 'Hỗ Trợ Nghiên Cứu KHKT - STEM' }
                ];

                courses.forEach(course => {
                    const option = document.createElement('option');
                    option.value = course.id;
                    option.textContent = course.name;
                    classFilter.appendChild(option);
                });

            } catch (error) {
                console.error('Error loading courses:', error);
            }
        }

        // Display registrations
        function displayRegistrations() {
            const container = document.getElementById('registrationsTableContainer');

            if (registrationsData.length === 0) {
                container.innerHTML = `
                    <div class="no-registrations">
                        <i class="fas fa-file-alt"></i>
                        <h3>Chưa có đơn đăng ký nào</h3>
                        <p>Danh sách đơn đăng ký sẽ hiển thị ở đây</p>
                    </div>
                `;
                return;
            }

            const filteredData = getFilteredRegistrations();

            if (filteredData.length === 0) {
                container.innerHTML = `
                    <div class="no-registrations">
                        <i class="fas fa-filter"></i>
                        <h3>Không có đơn đăng ký nào phù hợp</h3>
                        <p>Thử thay đổi bộ lọc để xem thêm đơn đăng ký</p>
                    </div>
                `;
                return;
            }

            let tableHTML = `
                <table class="registrations-table">
                    <thead>
                        <tr>
                            <th>Học viên</th>
                            <th>Phụ huynh</th>
                            <th>Khóa học quan tâm</th>
                            <th>Ngày đăng ký</th>
                            <th>Trạng thái</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            filteredData.forEach(registration => {
                const registrationDate = registration.registrationDate ?
                    new Date(registration.registrationDate).toLocaleDateString('vi-VN') : 'Không rõ';

                const courseName = registration.courseName || getCourseDisplayName(registration.course);
                const statusClass = `status-${registration.status}`;
                const statusText = getStatusText(registration.status);

                tableHTML += `
                    <tr>
                        <td>
                            <div class="student-info">
                                <img src="../../assets/images/avatars/avatar_boy_1.png" alt="Avatar" class="student-avatar">
                                <div class="student-details">
                                    <h4>${registration.studentName}</h4>
                                    <p>Sinh: ${registration.birthDate || 'Chưa cập nhật'}</p>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="parent-info">
                                <strong>${registration.parentName}</strong><br>
                                <span style="color: #666;">${registration.parentPhone}</span>
                            </div>
                        </td>
                        <td>${courseName}</td>
                        <td>${registrationDate}</td>
                        <td>
                            <span class="status-badge ${statusClass}">${statusText}</span>
                        </td>
                        <td>
                            <div class="action-buttons">
                                ${registration.status === 'pending' ? `
                                    <button class="action-btn approve-btn" onclick="updateRegistrationStatus('${registration.id}', 'approved')">
                                        <i class="fas fa-check"></i> Duyệt
                                    </button>
                                    <button class="action-btn reject-btn" onclick="updateRegistrationStatus('${registration.id}', 'rejected')">
                                        <i class="fas fa-times"></i> Từ chối
                                    </button>
                                ` : ''}
                                <button class="action-btn view-btn" onclick="viewRegistrationDetails('${registration.id}')">
                                    <i class="fas fa-eye"></i> Xem
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            });

            tableHTML += `
                    </tbody>
                </table>
            `;

            container.innerHTML = tableHTML;
        }

        // Get filtered registrations
        function getFilteredRegistrations() {
            const statusFilter = document.getElementById('statusFilter').value;
            const classFilter = document.getElementById('classFilter').value;
            const dateFilter = document.getElementById('dateFilter').value;

            let filtered = [...registrationsData];

            // Apply status filter
            if (statusFilter) {
                filtered = filtered.filter(reg => reg.status === statusFilter);
            }

            // Apply course filter (instead of class filter)
            if (classFilter) {
                filtered = filtered.filter(reg => reg.course === classFilter);
            }

            // Apply date filter
            if (dateFilter) {
                const now = new Date();
                const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

                filtered = filtered.filter(reg => {
                    if (!reg.registrationDate) return false;
                    const regDate = new Date(reg.registrationDate);

                    switch (dateFilter) {
                        case 'today':
                            return regDate >= today;
                        case 'week':
                            const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
                            return regDate >= weekAgo;
                        case 'month':
                            const monthAgo = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
                            return regDate >= monthAgo;
                        default:
                            return true;
                    }
                });
            }

            return filtered;
        }

        // Update registration status
        window.updateRegistrationStatus = async function(registrationId, newStatus) {
            const statusText = newStatus === 'approved' ? 'duyệt' : 'từ chối';

            if (!confirm(`Bạn có chắc chắn muốn ${statusText} đơn đăng ký này?`)) {
                return;
            }

            try {
                await updateDoc(doc(db, "course_registrations", registrationId), {
                    status: newStatus,
                    reviewedBy: '<EMAIL>',
                    reviewedAt: new Date().toISOString()
                });

                // Update local data
                const registration = registrationsData.find(r => r.id === registrationId);
                if (registration) {
                    registration.status = newStatus;
                }

                // Refresh display
                displayRegistrations();
                updateStats();

                showSuccess(`Đã ${statusText} đơn đăng ký thành công!`);

            } catch (error) {
                console.error('Error updating registration status:', error);
                showError('Lỗi khi cập nhật trạng thái đơn đăng ký');
            }
        };

        // View registration details
        window.viewRegistrationDetails = function(registrationId) {
            const registration = registrationsData.find(r => r.id === registrationId);
            if (!registration) return;

            const details = `
Thông tin đơn đăng ký tư vấn:

Học viên:
- Họ tên: ${registration.studentName || 'Chưa cập nhật'}
- Ngày sinh: ${registration.birthDate || 'Chưa cập nhật'}

Phụ huynh:
- Tên phụ huynh: ${registration.parentName || 'Chưa cập nhật'}
- SĐT phụ huynh: ${registration.parentPhone || 'Chưa cập nhật'}

Khóa học:
- Khóa học quan tâm: ${registration.courseName || getCourseDisplayName(registration.course)}
- Mã khóa học: ${registration.course || 'Chưa cập nhật'}

Thông tin đăng ký:
- Ngày đăng ký: ${registration.registrationDate ? new Date(registration.registrationDate).toLocaleString('vi-VN') : 'Không rõ'}
- Trạng thái: ${getStatusText(registration.status)}
- Người duyệt: ${registration.reviewedBy || 'Chưa duyệt'}
- Ngày duyệt: ${registration.reviewedAt ? new Date(registration.reviewedAt).toLocaleString('vi-VN') : 'Chưa duyệt'}
            `;

            alert(details);
        };

        // Update stats
        function updateStats() {
            const pending = registrationsData.filter(r => r.status === 'pending').length;
            const approved = registrationsData.filter(r => r.status === 'approved').length;
            const rejected = registrationsData.filter(r => r.status === 'rejected').length;
            const total = registrationsData.length;

            document.getElementById('pendingCount').textContent = pending;
            document.getElementById('approvedCount').textContent = approved;
            document.getElementById('rejectedCount').textContent = rejected;
            document.getElementById('totalCount').textContent = total;
        }

        // Setup event listeners
        function setupEventListeners() {
            document.getElementById('statusFilter').addEventListener('change', displayRegistrations);
            document.getElementById('classFilter').addEventListener('change', displayRegistrations);
            document.getElementById('dateFilter').addEventListener('change', displayRegistrations);
        }

        // Utility functions
        function getCourseDisplayName(courseId) {
            const courseMap = {
                'python': 'Python - AI từ Cơ Bản đến Nâng Cao',
                'scratch': 'Scratch - Tin Học Cơ Bản',
                'stem': 'Hỗ Trợ Nghiên Cứu KHKT - STEM'
            };
            return courseMap[courseId] || (courseId || 'Chưa chọn khóa học');
        }

        function getStatusText(status) {
            const statusMap = {
                'pending': 'Chờ duyệt',
                'approved': 'Đã duyệt',
                'rejected': 'Từ chối'
            };
            return statusMap[status] || 'Không rõ';
        }

        function showError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.textContent = message;

            const container = document.querySelector('.container');
            container.insertBefore(errorDiv, container.firstChild);

            setTimeout(() => {
                errorDiv.remove();
            }, 5000);
        }

        function showSuccess(message) {
            const successDiv = document.createElement('div');
            successDiv.className = 'success-message';
            successDiv.textContent = message;

            const container = document.querySelector('.container');
            container.insertBefore(successDiv, container.firstChild);

            setTimeout(() => {
                successDiv.remove();
            }, 5000);
        }
    </script>
</body>
</html>
