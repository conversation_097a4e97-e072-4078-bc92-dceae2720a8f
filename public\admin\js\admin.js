// Import Firebase modules
import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";
import {
    getFirestore,
    doc,
    getDoc,
    setDoc,
    collection,
    query,
    getDocs,
    where,
    orderBy,
    limit
} from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

// Firebase configuration
const firebaseConfig = {
    apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
    authDomain: "classroom-web-48bc2.firebaseapp.com",
    projectId: "classroom-web-48bc2",
    storageBucket: "classroom-web-48bc2.firebasestorage.app",
    messagingSenderId: "446746787502",
    appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
    measurementId: "G-742XRP9E96"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

// Check admin access
onAuthStateChanged(auth, async (user) => {
    if (!user || user.email !== '<EMAIL>') {
        // Redirect non-admin users
        window.location.href = '../auth/';
        return;
    }

    // Update admin info
    document.getElementById('adminName').textContent = user.displayName || 'Admin';
    document.getElementById('adminEmail').textContent = user.email;

    // Initialize dashboard
    await initializeDashboard();
});

// Initialize dashboard data
async function initializeDashboard() {
    try {
        // Initialize class settings if not exists
        await initializeClassSettings();
        
        // Load dashboard stats
        await loadDashboardStats();
        
        // Load recent activities
        await loadRecentActivities();
        
    } catch (error) {
        console.error('Error initializing dashboard:', error);
        showError('Lỗi khi tải dữ liệu dashboard');
    }
}

// Initialize class settings
async function initializeClassSettings() {
    const classes = [
        {
            classId: 'python-a',
            className: 'Python & AI - Lớp A',
            subject: 'python',
            fee: 250000,
            isRegistrationOpen: true,
            maxStudents: 10,
            currentStudents: 0
        },
        {
            classId: 'python-b',
            className: 'Python & AI - Lớp B',
            subject: 'python',
            fee: 250000,
            isRegistrationOpen: true,
            maxStudents: 10,
            currentStudents: 0
        },
        {
            classId: 'python-c',
            className: 'Python & AI - Lớp C',
            subject: 'python',
            fee: 250000,
            isRegistrationOpen: true,
            maxStudents: 10,
            currentStudents: 0
        },
        {
            classId: 'scratch-a',
            className: 'Scratch - Lớp A',
            subject: 'scratch',
            fee: 300000,
            isRegistrationOpen: true,
            maxStudents: 12,
            currentStudents: 0
        },
        {
            classId: 'scratch-b',
            className: 'Scratch - Lớp B',
            subject: 'scratch',
            fee: 300000,
            isRegistrationOpen: true,
            maxStudents: 12,
            currentStudents: 0
        },
        {
            classId: 'khkt-a',
            className: 'KHKT - Lớp A',
            subject: 'khkt',
            fee: 350000,
            isRegistrationOpen: true,
            maxStudents: 8,
            currentStudents: 0
        }
    ];

    for (const classData of classes) {
        const classDoc = await getDoc(doc(db, "classSettings", classData.classId));
        if (!classDoc.exists()) {
            await setDoc(doc(db, "classSettings", classData.classId), {
                ...classData,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            });
            console.log(`Initialized class settings for ${classData.classId}`);
        }
    }
}

// Load dashboard statistics
async function loadDashboardStats() {
    try {
        // Get total students
        const usersQuery = query(collection(db, "users"));
        const usersSnapshot = await getDocs(usersQuery);
        let totalStudents = 0;
        let studentsWithClasses = 0;

        usersSnapshot.forEach((doc) => {
            const userData = doc.data();
            if (userData.fullName && userData.fullName.trim() !== '' && userData.email !== '<EMAIL>') {
                totalStudents++;
                if (userData.courseClass || userData.allowedClasses) {
                    studentsWithClasses++;
                }
            }
        });

        // Get active classes
        const classesQuery = query(collection(db, "classSettings"));
        const classesSnapshot = await getDocs(classesQuery);
        let activeClasses = 0;

        classesSnapshot.forEach((doc) => {
            const classData = doc.data();
            if (classData.isRegistrationOpen) {
                activeClasses++;
            }
        });

        // Get fee payments data
        const feePaymentsQuery = query(collection(db, "feePayments"));
        const feePaymentsSnapshot = await getDocs(feePaymentsQuery);

        // Group payments by month to find the most relevant month
        const monthlyData = {};
        feePaymentsSnapshot.forEach((doc) => {
            const paymentData = doc.data();
            const month = paymentData.month;
            if (!monthlyData[month]) {
                monthlyData[month] = { paid: 0, total: 0, revenue: 0 };
            }
            monthlyData[month].total++;
            if (paymentData.isPaid) {
                monthlyData[month].paid++;
                monthlyData[month].revenue += paymentData.amount || 250000;
            }
        });

        // Prioritize current month, fallback to month with most activity
        const currentMonth = '2025-06'; // Force current month to June 2025
        let targetMonth = currentMonth;

        // If current month has no data, find month with most activity
        if (!monthlyData[currentMonth] || monthlyData[currentMonth].total === 0) {
            let maxActivity = 0;
            for (const [month, data] of Object.entries(monthlyData)) {
                const activity = data.paid + (data.total * 0.1); // Prioritize paid over total
                if (activity > maxActivity) {
                    maxActivity = activity;
                    targetMonth = month;
                }
            }
        }

        // Get stats for the target month
        const monthData = monthlyData[targetMonth] || { paid: 0, total: 0, revenue: 0 };
        const paidThisMonth = monthData.paid;
        const monthlyRevenue = monthData.revenue;

        // Calculate completion rate based on fee payments
        const completionRate = totalStudents > 0 ? Math.round((paidThisMonth / totalStudents) * 100) : 0;

        // Update UI
        document.getElementById('totalStudents').textContent = totalStudents;
        document.getElementById('totalClasses').textContent = '6';
        document.getElementById('monthlyRevenue').textContent = formatCurrency(monthlyRevenue);
        document.getElementById('completionRate').textContent = completionRate + '%';

        // Format target month for display
        const monthNames = {
            '01': 'tháng 1', '02': 'tháng 2', '03': 'tháng 3', '04': 'tháng 4',
            '05': 'tháng 5', '06': 'tháng 6', '07': 'tháng 7', '08': 'tháng 8',
            '09': 'tháng 9', '10': 'tháng 10', '11': 'tháng 11', '12': 'tháng 12'
        };
        const [year, month] = targetMonth.split('-');
        const monthDisplay = `${monthNames[month]} năm ${year}`;

        // Count pending registrations
        const pendingRegistrations = usersSnapshot.size > 0 ?
            Array.from(usersSnapshot.docs).filter(doc => {
                const userData = doc.data();
                return userData.email !== '<EMAIL>' &&
                       userData.fullName &&
                       (userData.registrationStatus === 'pending' || !userData.registrationStatus);
            }).length : 0;

        // Update module stats
        document.getElementById('studentsCount').textContent = `${totalStudents} học viên`;
        document.getElementById('activeClasses').textContent = `${activeClasses} lớp đang mở`;
        document.getElementById('pendingRegistrations').textContent = `${pendingRegistrations} đơn chờ duyệt`;

        // Show month info - prioritize "tháng này" for current month
        if (targetMonth === currentMonth) {
            document.getElementById('feeStats').textContent = `${paidThisMonth}/${totalStudents} đã đóng tháng này`;
        } else if (paidThisMonth > 0 || Object.keys(monthlyData).length > 0) {
            document.getElementById('feeStats').textContent = `${paidThisMonth}/${totalStudents} đã đóng ${monthDisplay}`;
        } else {
            document.getElementById('feeStats').textContent = `0/${totalStudents} đã đóng tháng này`;
        }

    } catch (error) {
        console.error('Error loading dashboard stats:', error);
    }
}

// Load recent activities
async function loadRecentActivities() {
    try {
        // Get recent user registrations
        const recentUsersQuery = query(
            collection(db, "users"),
            orderBy("createdAt", "desc"),
            limit(5)
        );
        const recentUsersSnapshot = await getDocs(recentUsersQuery);
        
        const activityList = document.getElementById('activityList');
        activityList.innerHTML = '';

        if (recentUsersSnapshot.empty) {
            activityList.innerHTML = `
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div class="activity-content">
                        <p>Chưa có hoạt động nào</p>
                        <span class="activity-time">Hôm nay</span>
                    </div>
                </div>
            `;
            return;
        }

        recentUsersSnapshot.forEach((doc) => {
            const userData = doc.data();
            if (userData.fullName && userData.email !== '<EMAIL>') {
                const activityItem = document.createElement('div');
                activityItem.className = 'activity-item';
                
                const createdDate = new Date(userData.createdAt);
                const timeAgo = getTimeAgo(createdDate);
                
                activityItem.innerHTML = `
                    <div class="activity-icon">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <div class="activity-content">
                        <p>Học viên mới: ${userData.fullName}</p>
                        <span class="activity-time">${timeAgo}</span>
                    </div>
                `;
                
                activityList.appendChild(activityItem);
            }
        });

    } catch (error) {
        console.error('Error loading recent activities:', error);
    }
}

// Utility functions
function formatCurrency(amount) {
    return new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
    }).format(amount);
}

function getTimeAgo(date) {
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);
    
    if (diffInSeconds < 60) return 'Vừa xong';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} phút trước`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} giờ trước`;
    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} ngày trước`;
    
    return date.toLocaleDateString('vi-VN');
}

function showError(message) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.textContent = message;
    
    const container = document.querySelector('.container');
    container.insertBefore(errorDiv, container.firstChild);
    
    setTimeout(() => {
        errorDiv.remove();
    }, 5000);
}

function showSuccess(message) {
    const successDiv = document.createElement('div');
    successDiv.className = 'success-message';
    successDiv.textContent = message;
    
    const container = document.querySelector('.container');
    container.insertBefore(successDiv, container.firstChild);
    
    setTimeout(() => {
        successDiv.remove();
    }, 5000);
}
