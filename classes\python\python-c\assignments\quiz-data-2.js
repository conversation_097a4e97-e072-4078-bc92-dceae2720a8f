// Quiz data for Assignment 2: Python Introduction and Environment Setup - Python C
const quizQuestions2 = [
    {
        question: "Ai là người đã tạo ra ngôn ngữ lập trình <PERSON>?",
        options: [
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>"
        ],
        correct: 1,
        explanation: "<PERSON> là nhà lập trình người <PERSON>, người đã phát triển và phát hành phiên bản đầu tiên của Python vào năm 1991."
    },
    {
        question: "Tên gọi \"Python\" được lấy cảm hứng từ đâu?",
        options: [
            "Một loài trăn lớn",
            "Một nhóm hài kịch người Anh tên là Monty Python",
            "Một nhân vật thần thoại Hy Lạp",
            "Tên của một dự án nghiên cứu vũ trụ"
        ],
        correct: 1,
        explanation: "<PERSON> van <PERSON> là một fan của nhóm hài kịch \"Monty Python's Flying Circus\" và đã đặt tên ngôn ngữ theo tên nhóm hài này."
    },
    {
        question: "Đuôi file (phần mở rộng) của một tệp mã nguồn Python là gì?",
        options: [
            ".txt",
            ".doc",
            ".py",
            ".exe"
        ],
        correct: 2,
        explanation: "Các tệp chứa mã lệnh Python thường có đuôi là .py, ví dụ hello.py."
    },
    {
        question: "Trong quá trình cài đặt Python trên Windows, tùy chọn nào sau đây được khuyến nghị CỰC KỲ QUAN TRỌNG cần phải đánh dấu tick?",
        options: [
            "\"Create a desktop icon\"",
            "\"Add Python to PATH\" (hoặc \"Add Python to environment variables\")",
            "\"Install for all users\"",
            "\"Download debugging symbols\""
        ],
        correct: 1,
        explanation: "Việc thêm Python vào PATH giúp hệ điều hành có thể tìm thấy và chạy trình thông dịch Python từ bất kỳ thư mục nào trong Command Prompt hoặc Terminal."
    },
    {
        question: "Visual Studio Code (VS Code) là gì?",
        options: [
            "Một hệ điều hành",
            "Một trình duyệt web",
            "Một trình soạn thảo mã (code editor)",
            "Một ngôn ngữ lập trình"
        ],
        correct: 2,
        explanation: "VS Code là một công cụ mạnh mẽ và phổ biến dùng để viết, quản lý và gỡ lỗi mã nguồn cho nhiều ngôn ngữ lập trình, bao gồm Python."
    },
    {
        question: "Để VS Code hỗ trợ tốt nhất cho việc lập trình Python, bạn cần cài đặt thêm gì?",
        options: [
            "Một trình duyệt web mới",
            "Một extension (tiện ích mở rộng) có tên \"Python\" do Microsoft phát triển",
            "Một bộ gõ tiếng Việt",
            "Phiên bản Java mới nhất"
        ],
        correct: 1,
        explanation: "Extension \"Python\" của Microsoft cung cấp các tính năng như gợi ý code, gỡ lỗi, kiểm tra cú pháp... cho Python trong VS Code."
    },
    {
        question: "Lệnh nào trong Python được sử dụng để hiển thị (in ra) một nội dung nào đó lên màn hình?",
        options: [
            "input()",
            "display()",
            "print()",
            "show()"
        ],
        correct: 2,
        explanation: "Hàm print() là hàm chuẩn trong Python dùng để xuất dữ liệu ra thiết bị đầu ra tiêu chuẩn, thường là màn hình."
    },
    {
        question: "Ký tự nào được sử dụng để bắt đầu một dòng comment (ghi chú) trong Python?",
        options: [
            "//",
            "/*",
            "#",
            "--"
        ],
        correct: 2,
        explanation: "Trong Python, bất kỳ nội dung nào sau dấu # trên cùng một dòng đều được coi là comment và sẽ bị trình thông dịch bỏ qua."
    },
    {
        question: "Chương trình \"Hello, World!\" thường được dùng để làm gì?",
        options: [
            "Để kiểm tra tốc độ của máy tính",
            "Để kiểm tra xem môi trường lập trình đã được cài đặt và cấu hình đúng chưa",
            "Để gửi lời chào đến tất cả người dùng trên thế giới",
            "Để tạo ra một virus máy tính đơn giản"
        ],
        correct: 1,
        explanation: "Đây là một chương trình cơ bản và đơn giản, giúp người lập trình xác nhận rằng trình thông dịch và các công cụ liên quan đang hoạt động chính xác."
    },
    {
        question: "Google Colaboratory (Google Colab) là một môi trường lập trình Python hoạt động ở đâu?",
        options: [
            "Chỉ trên máy tính hệ điều hành Windows",
            "Chỉ trên máy tính hệ điều hành macOS",
            "Trực tiếp trên trình duyệt web (online)",
            "Chỉ có thể cài đặt từ đĩa CD"
        ],
        correct: 2,
        explanation: "Google Colab là một dịch vụ dựa trên đám mây, cho phép người dùng viết và chạy code Python thông qua trình duyệt web mà không cần cài đặt cục bộ."
    },
    {
        question: "Một trong những ưu điểm chính của Python khiến nó phù hợp cho người mới bắt đầu là gì?",
        options: [
            "Cú pháp rất phức tạp và khó nhớ",
            "Chỉ có thể dùng để phát triển game",
            "Cú pháp rõ ràng, dễ đọc, gần với ngôn ngữ tự nhiên",
            "Yêu cầu phần cứng máy tính rất cao"
        ],
        correct: 2,
        explanation: "Python được thiết kế với triết lý dễ đọc và dễ viết, giúp người mới học tập trung vào logic thay vì các chi tiết cú pháp phức tạp."
    },
    {
        question: "File notebook trong Google Colab thường có đuôi là gì?",
        options: [
            ".py",
            ".colab",
            ".ipynb",
            ".gdoc"
        ],
        correct: 2,
        explanation: ".ipynb là viết tắt của IPython Notebook, định dạng file chuẩn cho các notebook tương tác như Jupyter Notebook và Google Colab."
    },
    {
        question: "Lợi ích nào sau đây KHÔNG phải là của Google Colab?",
        options: [
            "Không cần cài đặt Python và thư viện",
            "Yêu cầu kết nối Internet rất chậm để hoạt động",
            "Cung cấp quyền truy cập GPU/TPU miễn phí (có giới hạn)",
            "Dễ dàng chia sẻ và hợp tác trên notebook"
        ],
        correct: 1,
        explanation: "Google Colab là một dịch vụ trực tuyến, do đó cần có kết nối Internet ổn định để hoạt động hiệu quả. Kết nối chậm sẽ ảnh hưởng đến trải nghiệm."
    },
    {
        question: "Khi bạn chạy một ô code (code cell) trong Google Colab, code đó thực sự được thực thi ở đâu?",
        options: [
            "Trên máy tính cá nhân của bạn",
            "Trên máy chủ của Google (trong đám mây)",
            "Trong bộ nhớ của trình duyệt web",
            "Trên một thiết bị USB bạn cắm vào"
        ],
        correct: 1,
        explanation: "Google Colab cung cấp một môi trường thực thi trên các máy chủ của Google, nơi code Python của bạn được chạy."
    },
    {
        question: "Trong khóa học này, môi trường nào được xác định là môi trường chính để học tập và thực hành Python?",
        options: [
            "Chỉ Google Colab",
            "Visual Studio Code (VS Code) và Python cài đặt trên máy tính cá nhân",
            "Chỉ trình soạn thảo Notepad",
            "Một ứng dụng Python trên điện thoại di động"
        ],
        correct: 1,
        explanation: "Theo như mục tiêu và nội dung bài học, VS Code và Python cài đặt cục bộ là môi trường chính, còn Google Colab là công cụ bổ trợ."
    }
];

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = quizQuestions2;
}
