<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bài Tập 3: <PERSON><PERSON>, <PERSON><PERSON><PERSON> và Chuỗi - Python C</title>
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="../../../../assets/images/favicon.png">
    <link rel="shortcut icon" type="image/png" href="../../../../assets/images/favicon.png">
    <link rel="apple-touch-icon" href="../../../../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="72x72" href="../../../../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="114x114" href="../../../../assets/images/favicon.png">

    <link rel="stylesheet" href="../../../../assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        /* Dark Background with Starfield Effect */
        body {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: white;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        /* Shooting stars animation */
        .shooting-star {
            position: absolute;
            width: 2px;
            height: 2px;
            background: #FFD700;
            border-radius: 50%;
            box-shadow: 0 0 10px #FFD700;
            animation: shoot 3s linear infinite;
        }

        @keyframes shoot {
            0% {
                transform: translateX(-100px) translateY(100px);
                opacity: 1;
            }
            100% {
                transform: translateX(100vw) translateY(-100vh);
                opacity: 0;
            }
        }

        /* Floating particles */
        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 215, 0, 0.6);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .assignment-container {
            max-width: 1000px;
            margin: 120px auto 50px;
            padding: 0 20px;
            position: relative;
            z-index: 1;
        }

        .assignment-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 25px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 215, 0, 0.3);
            color: white;
            border-radius: 15px;
        }

        .assignment-header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
            color: #FFD700;
        }

        .timer-info {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 215, 0, 0.3);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
        }

        .timer {
            font-size: 1.5rem;
            font-weight: bold;
            color: #FFD700;
        }

        .quiz-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
            padding: 30px;
            margin-bottom: 30px;
        }

        .question {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 10px;
            display: none;
            background: rgba(255, 255, 255, 0.05);
        }

        .question.active {
            display: block;
        }

        .question-number {
            color: #FFD700;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .question-text {
            font-size: 1.1rem;
            margin-bottom: 20px;
            line-height: 1.6;
            color: white;
        }

        .options {
            list-style: none;
            padding: 0;
        }

        .option {
            margin-bottom: 10px;
            padding: 15px;
            border: 2px solid rgba(255, 215, 0, 0.3);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            background: rgba(255, 255, 255, 0.05);
            color: white;
        }

        .option:hover {
            border-color: #FFD700;
            background-color: rgba(255, 215, 0, 0.1);
        }

        .option.selected {
            border-color: #FFD700;
            background-color: rgba(255, 215, 0, 0.2);
        }

        .option input[type="radio"] {
            margin-right: 10px;
        }

        .navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 30px;
            padding: 20px 0;
        }

        .nav-btn {
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            color: #1a1a2e;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s;
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
        }

        .nav-btn:disabled {
            background: rgba(255, 255, 255, 0.2);
            color: rgba(255, 255, 255, 0.5);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            border-radius: 4px;
            transition: width 0.3s;
        }

        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(40px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }

        .question-indicator {
            width: 40px;
            height: 40px;
            border: 2px solid rgba(255, 215, 0, 0.3);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: bold;
            background: rgba(255, 255, 255, 0.05);
            color: white;
        }

        .question-indicator.answered {
            background: #FFD700;
            color: #1a1a2e;
            border-color: #FFD700;
        }

        .question-indicator.current {
            border-color: #FFA500;
            background: #FFA500;
            color: #1a1a2e;
        }

        .submit-section {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(255, 215, 0, 0.3);
        }

        .submit-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.1rem;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        }

        .back-link {
            display: inline-block;
            color: #FFD700;
            text-decoration: none;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .back-link:hover {
            text-decoration: underline;
            color: #FFA500;
        }

        .results-container {
            display: none;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
            padding: 30px;
            margin-bottom: 30px;
        }

        .score-display {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(255, 215, 0, 0.3);
        }

        .score-number {
            font-size: 3rem;
            font-weight: bold;
            color: #FFD700;
        }

        .review-question {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.05);
        }

        .correct-answer {
            color: #28a745;
            font-weight: bold;
        }

        .wrong-answer {
            color: #dc3545;
            font-weight: bold;
        }

        .explanation {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            margin-top: 10px;
            font-style: italic;
            color: rgba(255, 255, 255, 0.9);
        }
    </style>
</head>
<body>
    <!-- Shooting stars -->
    <div class="shooting-star" style="top: 20%; animation-delay: 0s;"></div>
    <div class="shooting-star" style="top: 40%; animation-delay: 2s;"></div>
    <div class="shooting-star" style="top: 60%; animation-delay: 4s;"></div>
    <div class="shooting-star" style="top: 80%; animation-delay: 6s;"></div>

    <!-- Floating particles -->
    <div class="particle" style="top: 10%; left: 10%; animation-delay: 0s;"></div>
    <div class="particle" style="top: 20%; left: 80%; animation-delay: 1s;"></div>
    <div class="particle" style="top: 70%; left: 20%; animation-delay: 2s;"></div>
    <div class="particle" style="top: 80%; left: 70%; animation-delay: 3s;"></div>

    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../../../../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../../../../index.html">Trang Chủ</a></li>
                    <li><a href="../../../index.html">Lớp Học</a></li>
                    <li><a href="../../../../achievements/">Thành Tích</a></li>
                    <li><a href="../../../../auth/register.html">Đăng Ký</a></li>
                    <li><a href="../../../../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../../../../research/">Nghiên Cứu</a></li>
                    <li><a href="../../../../auth/">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <div class="assignment-container">
        <a href="../python-c.html" class="back-link">
            <i class="fas fa-arrow-left"></i> Quay lại lớp Python - C
        </a>

        <div class="assignment-header">
            <h1>Bài Tập 3: Dữ Liệu – Biến, Kiểu Số và Chuỗi</h1>
            <p>50 câu hỏi - Thời gian: 75 phút</p>
        </div>

        <div class="timer-info">
            <div>
                <strong>Thời gian còn lại:</strong>
                <span class="timer" id="timer">Đang kiểm tra...</span>
            </div>
            <div>
                <strong>Câu hỏi:</strong>
                <span id="current-question">-</span> / <span id="total-questions">50</span>
            </div>
        </div>

        <!-- Quiz Container -->
        <div class="quiz-container" id="quizContainer">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>

            <div class="question-grid" id="questionGrid">
                <!-- Question indicators will be generated here -->
            </div>

            <div id="questionsContainer">
                <!-- Questions will be loaded here -->
            </div>

            <div class="navigation">
                <button class="nav-btn" id="prevBtn" onclick="previousQuestion()" disabled>
                    <i class="fas fa-chevron-left"></i> Câu trước
                </button>

                <div>
                    <span id="questionStatus">Câu 1 / 50</span>
                </div>

                <button class="nav-btn" id="nextBtn" onclick="nextQuestion()">
                    Câu tiếp <i class="fas fa-chevron-right"></i>
                </button>
            </div>

            <div class="submit-section">
                <button class="submit-btn" id="submitBtn" onclick="submitQuiz()">
                    <i class="fas fa-check"></i> Nộp Bài
                </button>
            </div>
        </div>

        <!-- Results Container -->
        <div class="results-container" id="resultsContainer">
            <div class="score-display">
                <div class="score-number" id="finalScore">0</div>
                <div>điểm / 100 điểm</div>
                <div>Thời gian hoàn thành: <span id="completionTime"></span></div>
            </div>

            <div id="reviewContainer">
                <!-- Review will be shown here -->
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../../../../assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";
        import { getFirestore, doc, getDoc, updateDoc, setDoc } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        // Quiz data will be loaded here
        let quizData = [];
        let currentQuestionIndex = 0;
        let userAnswers = {};
        let timeLeft = 75 * 60; // 75 minutes in seconds
        let timerInterval;
        let startTime;

        // Initialize quiz when page loads
        window.addEventListener('DOMContentLoaded', function() {
            checkAssignmentCompletion();
        });

        // Global variables for quiz state
        let quizStarted = false;
        let quizCompleted = false;

        // Setup anti-cheat mechanism
        function setupAntiCheat() {
            // Track page visibility changes
            document.addEventListener('visibilitychange', function() {
                if (quizStarted && !quizCompleted && document.hidden) {
                    console.log('User left page during quiz - marking as cheating attempt');
                    handleCheatingAttempt();
                }
            });

            // Track focus changes with delay to avoid false positives from alerts
            window.addEventListener('blur', function() {
                if (quizStarted && !quizCompleted) {
                    setTimeout(() => {
                        if (quizStarted && !quizCompleted) {
                            console.log('Window lost focus during quiz');
                            handleCheatingAttempt();
                        }
                    }, 100);
                }
            });
        }

        // Handle cheating attempt
        async function handleCheatingAttempt() {
            if (quizCompleted) return;

            quizCompleted = true;
            clearInterval(timerInterval);

            // Save 0 score immediately
            await saveZeroScore();

            // Show cheating detection message
            showCheatingDetected();
        }

        // Save zero score when cheating detected
        async function saveZeroScore() {
            const user = auth.currentUser;
            if (!user || isAdmin(user)) return;

            try {
                const results = [];
                if (quizData && quizData.length > 0) {
                    quizData.forEach((question, index) => {
                        results.push({
                            question: question.question,
                            options: question.options,
                            userAnswer: -1,
                            correctAnswer: question.correct,
                            isCorrect: false,
                            explanation: question.explanation
                        });
                    });
                }

                await setDoc(doc(db, "users", user.uid, "assignments", "assignment-3"), {
                    score: 0,
                    totalQuestions: quizData ? quizData.length : 50,
                    completionTime: "0 phút 0 giây",
                    timestamp: new Date(),
                    results: results,
                    cheatingDetected: true,
                    reason: 'Left page during quiz'
                });

                console.log('Zero score saved due to cheating detection');
            } catch (error) {
                console.error('Error saving zero score:', error);
            }
        }

        // Show cheating detected message
        function showCheatingDetected() {
            document.getElementById('quizContainer').style.display = 'none';
            document.getElementById('resultsContainer').style.display = 'block';

            const resultsContainer = document.getElementById('resultsContainer');
            resultsContainer.innerHTML = `
                <div style="background: linear-gradient(135deg, #dc3545, #c82333); color: white; padding: 20px; margin-bottom: 20px; border-radius: 8px; text-align: center; font-weight: bold; border: 2px solid #bd2130;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 10px;"></i>
                    <h3>Vi phạm quy định làm bài!</h3>
                    <p>Hệ thống đã phát hiện bạn rời khỏi trang trong quá trình làm bài.</p>
                    <p><strong>Kết quả: 0 điểm - Bài tập đã được đánh dấu hoàn thành</strong></p>
                    <p>Bạn có thể xem đáp án để ôn tập, nhưng không thể làm lại.</p>
                </div>
                <div style="text-align: center; margin: 20px 0;">
                    <a href="../python-c.html" style="display: inline-block; background: #6c757d; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; font-weight: bold;">
                        <i class="fas fa-arrow-left"></i> Quay lại lớp học
                    </a>
                </div>
            `;
        }

        // Check if user is admin
        function isAdmin(user) {
            return user && user.email === '<EMAIL>';
        }

        // Show confirmation before starting quiz
        function showQuizStartConfirmation() {
            return new Promise((resolve) => {
                // Create modal overlay
                const overlay = document.createElement('div');
                overlay.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.5);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 10000;
                `;

                // Create modal content
                const modal = document.createElement('div');
                modal.style.cssText = `
                    background: white;
                    padding: 30px;
                    border-radius: 15px;
                    max-width: 500px;
                    text-align: center;
                    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                `;

                modal.innerHTML = `
                    <div style="margin-bottom: 20px;">
                        <i class="fas fa-clock" style="font-size: 48px; color: #ffc107; margin-bottom: 15px;"></i>
                        <h3 style="margin: 0 0 15px 0; color: #333;">Xác nhận bắt đầu làm bài</h3>
                        <p style="margin: 0; color: #666; line-height: 1.6;">
                            Bạn chắc chắn vào làm Trắc Nghiệm chứ?<br>
                            Một khi đã vào thì sẽ bắt đầu tính giờ làm bài,<br>
                            hãy lưu ý làm theo sự hướng dẫn của Giáo Viên.
                        </p>
                    </div>
                    <div style="display: flex; gap: 15px; justify-content: center;">
                        <button id="cancelQuiz" style="
                            background: #6c757d;
                            color: white;
                            border: none;
                            padding: 12px 24px;
                            border-radius: 8px;
                            cursor: pointer;
                            font-size: 16px;
                        ">Hủy bỏ</button>
                        <button id="startQuiz" style="
                            background: #28a745;
                            color: white;
                            border: none;
                            padding: 12px 24px;
                            border-radius: 8px;
                            cursor: pointer;
                            font-size: 16px;
                        ">Bắt đầu làm bài</button>
                    </div>
                `;

                overlay.appendChild(modal);
                document.body.appendChild(overlay);

                // Handle button clicks
                document.getElementById('cancelQuiz').onclick = () => {
                    document.body.removeChild(overlay);
                    resolve(false);
                };

                document.getElementById('startQuiz').onclick = () => {
                    document.body.removeChild(overlay);
                    resolve(true);
                };
            });
        }

        // Check if assignment is already completed
        async function checkAssignmentCompletion() {
            onAuthStateChanged(auth, async (user) => {
                if (!user) {
                    // User not logged in, redirect to login
                    alert('Bạn cần đăng nhập để làm bài tập!');
                    window.location.href = '../../../auth/';
                    return;
                }

                try {
                    // Check if assignment is already completed
                    const assignmentDoc = await getDoc(doc(db, "users", user.uid, "assignments", "assignment-3"));

                    if (assignmentDoc.exists()) {
                        // Assignment already completed, show results
                        const data = assignmentDoc.data();
                        showCompletedAssignment(data);
                        return;
                    }

                    // Show confirmation dialog
                    const shouldStart = await showQuizStartConfirmation();
                    if (!shouldStart) {
                        window.location.href = '../python-c.html';
                        return;
                    }

                    // Start the quiz
                    await loadQuizData();
                    startQuiz();
                } catch (error) {
                    console.error('Error checking assignment completion:', error);
                    alert('Có lỗi xảy ra khi kiểm tra bài tập. Vui lòng thử lại!');
                }
            });
        }

        function showCompletedAssignment(data) {
            document.getElementById('quizContainer').style.display = 'none';
            document.getElementById('resultsContainer').style.display = 'block';

            document.getElementById('finalScore').textContent = data.score || 0;
            document.getElementById('completionTime').textContent = data.completionTime || 'Không xác định';

            // Show review if available
            if (data.results) {
                showReview(data.results);
            }
        }

        function startQuiz() {
            quizStarted = true;
            startTime = new Date();
            setupAntiCheat();
            startTimer();
            generateQuestionGrid();
            displayQuestion(0);
        }

        function startTimer() {
            timerInterval = setInterval(() => {
                timeLeft--;
                updateTimerDisplay();

                if (timeLeft <= 0) {
                    clearInterval(timerInterval);
                    submitQuiz();
                }
            }, 1000);
        }

        function updateTimerDisplay() {
            const minutes = Math.floor(timeLeft / 60);
            const seconds = timeLeft % 60;
            document.getElementById('timer').textContent =
                `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }

        function generateQuestionGrid() {
            const grid = document.getElementById('questionGrid');
            grid.innerHTML = '';

            for (let i = 0; i < quizData.length; i++) {
                const indicator = document.createElement('div');
                indicator.className = 'question-indicator';
                indicator.textContent = i + 1;
                indicator.onclick = () => displayQuestion(i);
                indicator.id = `indicator-${i}`;
                grid.appendChild(indicator);
            }

            updateQuestionIndicators();
        }

        window.selectAnswer = function(questionIndex, answerIndex) {
            userAnswers[questionIndex] = answerIndex;

            // Update visual selection
            const options = document.querySelectorAll(`input[name="question-${questionIndex}"]`);
            options.forEach((option, index) => {
                const li = option.parentElement;
                if (index === answerIndex) {
                    li.classList.add('selected');
                    option.checked = true;
                } else {
                    li.classList.remove('selected');
                    option.checked = false;
                }
            });

            updateQuestionIndicators();
            updateProgress();
        }

        function displayQuestion(index) {
            currentQuestionIndex = index;
            const container = document.getElementById('questionsContainer');
            const question = quizData[index];

            container.innerHTML = `
                <div class="question active">
                    <div class="question-number">Câu ${index + 1}/${quizData.length}</div>
                    <div class="question-text">${question.question}</div>
                    <ul class="options">
                        ${question.options.map((option, optIndex) => `
                            <li class="option ${userAnswers[index] === optIndex ? 'selected' : ''}"
                                onclick="selectAnswer(${index}, ${optIndex})">
                                <input type="radio" name="question-${index}" value="${optIndex}"
                                       ${userAnswers[index] === optIndex ? 'checked' : ''}>
                                ${String.fromCharCode(65 + optIndex)}. ${option}
                            </li>
                        `).join('')}
                    </ul>
                </div>
            `;

            updateNavigation();
            updateQuestionIndicators();
            updateProgress();
        }

        function updateNavigation() {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            const questionStatus = document.getElementById('questionStatus');

            prevBtn.disabled = currentQuestionIndex === 0;
            nextBtn.disabled = currentQuestionIndex === quizData.length - 1;

            questionStatus.textContent = `Câu ${currentQuestionIndex + 1} / ${quizData.length}`;

            document.getElementById('current-question').textContent = currentQuestionIndex + 1;
        }

        function updateQuestionIndicators() {
            for (let i = 0; i < quizData.length; i++) {
                const indicator = document.getElementById(`indicator-${i}`);
                indicator.classList.remove('answered', 'current');

                if (i === currentQuestionIndex) {
                    indicator.classList.add('current');
                } else if (userAnswers.hasOwnProperty(i)) {
                    indicator.classList.add('answered');
                }
            }
        }

        function updateProgress() {
            const answeredCount = Object.keys(userAnswers).length;
            const progressPercentage = (answeredCount / quizData.length) * 100;
            document.getElementById('progressFill').style.width = `${progressPercentage}%`;
        }

        window.previousQuestion = function() {
            if (currentQuestionIndex > 0) {
                displayQuestion(currentQuestionIndex - 1);
            }
        }

        window.nextQuestion = function() {
            if (currentQuestionIndex < quizData.length - 1) {
                displayQuestion(currentQuestionIndex + 1);
            }
        }

        window.submitQuiz = async function() {
            if (quizCompleted) return;

            quizCompleted = true;
            clearInterval(timerInterval);

            const endTime = new Date();
            const completionTime = Math.round((endTime - startTime) / 1000);
            const completionTimeFormatted = formatTime(completionTime);

            // Calculate score
            let correctAnswers = 0;
            const results = [];

            for (let i = 0; i < quizData.length; i++) {
                const userAnswer = userAnswers[i];
                const correctAnswer = quizData[i].correct;
                const isCorrect = userAnswer === correctAnswer;

                if (isCorrect) correctAnswers++;

                results.push({
                    question: quizData[i].question || '',
                    options: quizData[i].options || [],
                    userAnswer: userAnswer !== undefined ? userAnswer : -1,
                    correctAnswer: correctAnswer || 0,
                    isCorrect: isCorrect,
                    explanation: quizData[i].explanation || ''
                });
            }

            const score = Math.round((correctAnswers / quizData.length) * 100); // Convert to 100-point scale

            // Save to Firebase
            try {
                const user = auth.currentUser;
                if (user && !isAdmin(user)) {
                    // Validate and sanitize all data before saving
                    const sanitizedScore = typeof score === 'number' && !isNaN(score) ? score : 0;
                    const sanitizedResults = Array.isArray(results) ? results.filter(result =>
                        result &&
                        typeof result.question === 'string' &&
                        Array.isArray(result.options) &&
                        typeof result.correctAnswer === 'number' &&
                        typeof result.isCorrect === 'boolean' &&
                        typeof result.explanation === 'string' &&
                        typeof result.userAnswer === 'number'
                    ).map(result => ({
                        question: result.question || '',
                        options: result.options || [],
                        userAnswer: result.userAnswer,
                        correctAnswer: result.correctAnswer,
                        isCorrect: result.isCorrect,
                        explanation: result.explanation || ''
                    })) : [];

                    await setDoc(doc(db, "users", user.uid, "assignments", "assignment-3"), {
                        score: sanitizedScore,
                        totalQuestions: quizData.length || 50,
                        completionTime: completionTimeFormatted || "0 phút 0 giây",
                        timestamp: new Date(),
                        results: sanitizedResults
                    });

                    // Update user's assignment count and total score for ranking
                    const userDoc = await getDoc(doc(db, "users", user.uid));
                    if (userDoc.exists()) {
                        const userData = userDoc.data();
                        const currentTotalScore = userData.totalScore || 0;
                        const newTotalScore = currentTotalScore + sanitizedScore;
                        const sanitizedCompletionTime = completionTime || 0; // Add missing variable

                        await updateDoc(doc(db, "users", user.uid), {
                            assignmentCount: (userData.assignmentCount || 0) + 1,
                            totalScore: newTotalScore,
                            lastAssignmentScore: sanitizedScore,
                            lastAssignmentDate: new Date().toISOString(),
                            totalCompletionTime: (userData.totalCompletionTime || 0) + sanitizedCompletionTime
                        });

                        console.log(`Updated total score: ${currentTotalScore} + ${sanitizedScore} = ${newTotalScore} (Assignment 3)`);
                    }

                    console.log('Assignment saved successfully');
                } else if (isAdmin(user)) {
                    console.log('🔧 Admin mode: Assignment completed but results not saved');
                }
            } catch (error) {
                console.error('Error saving assignment:', error);
                alert('Có lỗi xảy ra khi lưu bài tập. Vui lòng thử lại!');
                return;
            }

            // Show results
            showResults(score, completionTimeFormatted, results);
        }

        function formatTime(seconds) {
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = seconds % 60;
            return `${minutes} phút ${remainingSeconds} giây`;
        }

        function showResults(score, completionTime, results) {
            document.getElementById('quizContainer').style.display = 'none';
            document.getElementById('resultsContainer').style.display = 'block';

            const user = auth.currentUser;

            // Add notification based on user type
            const resultsContainer = document.getElementById('resultsContainer');
            if (isAdmin(user)) {
                const adminNotification = document.createElement('div');
                adminNotification.style.cssText = `
                    background: linear-gradient(135deg, #e74c3c, #c0392b);
                    color: white;
                    padding: 15px;
                    margin-bottom: 20px;
                    border-radius: 8px;
                    text-align: center;
                    font-weight: bold;
                    border: 2px solid #a93226;
                `;
                adminNotification.innerHTML = `
                    <i class="fas fa-user-shield"></i>
                    <strong>Chế độ Admin:</strong> Bạn đã hoàn thành bài tập nhưng kết quả không được lưu vào hệ thống.
                `;
                resultsContainer.insertBefore(adminNotification, resultsContainer.firstChild);
            } else {
                const successNotice = document.createElement('div');
                successNotice.style.cssText = `
                    background: linear-gradient(135deg, #28a745, #20c997);
                    color: white;
                    padding: 15px;
                    margin-bottom: 20px;
                    border-radius: 8px;
                    text-align: center;
                    font-weight: bold;
                    border: 2px solid #1e7e34;
                `;
                successNotice.innerHTML = `
                    <i class="fas fa-check-circle"></i>
                    <strong>Nộp bài thành công!</strong> Điểm số đã được cập nhật.<br>
                    <small>Đang chuyển về trang lớp học trong 3 giây...</small>
                `;
                resultsContainer.insertBefore(successNotice, resultsContainer.firstChild);

                // Auto redirect after 3 seconds for regular users
                setTimeout(() => {
                    window.location.href = '../python-c.html';
                }, 3000);
            }

            document.getElementById('finalScore').textContent = score;
            document.getElementById('completionTime').textContent = completionTime;

            showReview(results);
        }

        function showReview(results) {
            const reviewContainer = document.getElementById('reviewContainer');

            if (!results || results.length === 0) {
                reviewContainer.innerHTML = '<p>Không có dữ liệu xem lại.</p>';
                return;
            }

            let reviewHTML = '<h3>Xem lại đáp án:</h3>';

            if (Array.isArray(results)) {
                reviewHTML += results.map((result, index) => {
                    return `
                        <div class="review-question">
                            <div class="question-number">Câu ${index + 1}</div>
                            <div class="question-text">${result.question}</div>
                            <div class="options">
                                ${result.options.map((option, optIndex) => {
                                    let optionClass = '';
                                    if (optIndex === result.correctAnswer) {
                                        optionClass = 'correct-answer';
                                    } else if (optIndex === result.userAnswer && !result.isCorrect) {
                                        optionClass = 'wrong-answer';
                                    }

                                    return `<div class="${optionClass}">
                                        ${String.fromCharCode(65 + optIndex)}. ${option}
                                        ${optIndex === result.correctAnswer ? ' ✓' : ''}
                                        ${optIndex === result.userAnswer && !result.isCorrect ? ' ✗' : ''}
                                    </div>`;
                                }).join('')}
                            </div>
                            <div class="explanation">
                                <strong>Giải thích:</strong> ${result.explanation}
                            </div>
                        </div>
                    `;
                });
            }

            reviewContainer.innerHTML = reviewHTML;
        }

        // Load quiz data from external file
        async function loadQuizData() {
            try {
                // Import quiz data
                const module = await import('./quiz-data-3.js');
                quizData = module.default || quizData;
            } catch (error) {
                console.error('Error loading quiz data:', error);
                alert('Không thể tải dữ liệu câu hỏi. Vui lòng thử lại!');
                return;
            }

            document.getElementById('total-questions').textContent = quizData.length;
        }
    </script>

    <script>
        // Create more shooting stars dynamically
        function createShootingStar() {
            const star = document.createElement('div');
            star.className = 'shooting-star';
            star.style.top = Math.random() * 100 + '%';
            star.style.animationDelay = Math.random() * 3 + 's';
            document.body.appendChild(star);

            setTimeout(() => {
                star.remove();
            }, 3000);
        }

        // Create shooting stars periodically
        setInterval(createShootingStar, 2000);

        // Create floating particles
        function createParticle() {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.top = Math.random() * 100 + '%';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.animationDelay = Math.random() * 6 + 's';
            document.body.appendChild(particle);

            setTimeout(() => {
                particle.remove();
            }, 6000);
        }

        // Create particles periodically
        setInterval(createParticle, 1000);
    </script>
</body>
</html>
