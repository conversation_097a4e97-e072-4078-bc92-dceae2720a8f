<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KHKT - A | Classroom Web</title>
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="../../../assets/images/favicon.png">
    <link rel="shortcut icon" type="image/png" href="../../../assets/images/favicon.png">
    <link rel="apple-touch-icon" href="../../../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="72x72" href="../../../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="114x114" href="../../../assets/images/favicon.png">

    <link rel="stylesheet" href="../../../assets/css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Dark Background with Starfield Effect */
        body {
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        /* Starfield Background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(2px 2px at 20px 30px, #fff, transparent),
                radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
                radial-gradient(1px 1px at 90px 40px, #fff, transparent),
                radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
                radial-gradient(2px 2px at 160px 30px, #fff, transparent);
            background-repeat: repeat;
            background-size: 200px 100px;
            animation: sparkle 3s linear infinite;
            opacity: 0.3;
            z-index: -1;
        }

        @keyframes sparkle {
            0% { transform: translateY(0px); }
            100% { transform: translateY(-100px); }
        }

        .class-header {
            background: transparent;
            color: white;
            padding: 60px 0;
            text-align: center;
            margin-top: 80px;
            position: relative;
            z-index: 1;
        }

        .class-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            word-wrap: break-word;
        }

        .class-header p {
            font-size: 1.1rem;
            opacity: 0.9;
            word-wrap: break-word;
        }

        @media (max-width: 768px) {
            .class-header h1 {
                font-size: 2rem;
            }
            .class-header p {
                font-size: 1rem;
            }
        }

        .class-info {
            background: transparent;
            padding: 40px 0;
            position: relative;
            z-index: 1;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .info-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }

        .info-card:hover {
            background: rgba(255, 255, 255, 0.08);
            transform: translateY(-5px);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.4);
        }

        .info-card i {
            font-size: 2.5rem;
            color: #FFD700;
            margin-bottom: 15px;
            text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
        }

        .info-card h3 {
            color: white;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        .info-card p {
            color: rgba(255, 255, 255, 0.9);
        }

        .access-denied {
            background: rgba(244, 67, 54, 0.1);
            border: 1px solid rgba(244, 67, 54, 0.3);
            color: #ff6b6b;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin: 20px 0;
            backdrop-filter: blur(10px);
        }

        .access-granted {
            background: rgba(76, 175, 80, 0.1);
            border: 1px solid rgba(76, 175, 80, 0.3);
            color: #4caf50;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin: 20px 0;
            backdrop-filter: blur(10px);
        }

        .lessons-section {
            background: transparent;
            padding: 40px 0;
            position: relative;
            z-index: 1;
        }

        .lessons-navigation {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .lesson-tab {
            background: white;
            border: 2px solid #667eea;
            color: #667eea;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            min-width: 120px;
            text-align: center;
        }

        .lesson-tab:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }

        .lesson-tab.active {
            background: #667eea;
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .lesson-content {
            display: none;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            animation: fadeIn 0.3s ease-in-out;
        }

        .lesson-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .lesson-content h3 {
            color: #667eea;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .lesson-objectives {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
        }

        .lesson-objectives h4 {
            color: #333;
            margin-bottom: 10px;
        }

        .lesson-objectives ul {
            margin: 10px 0;
            padding-left: 20px;
        }

        .lesson-objectives li {
            margin-bottom: 8px;
            line-height: 1.5;
        }

        .lesson-details {
            margin: 20px 0;
        }

        .lesson-details h4 {
            color: #667eea;
            margin: 15px 0 10px 0;
        }

        .lesson-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .status-info {
            flex: 1;
        }

        .assignment-button {
            margin-left: 20px;
        }

        .meet-link {
            display: inline-block;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            text-decoration: none;
            margin-top: 15px;
            transition: transform 0.3s ease;
        }

        .meet-link:hover {
            transform: translateY(-2px);
        }

        .students-display {
            margin-top: 20px;
        }

        .student-avatars {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }

        .student-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 18px;
            position: relative;
            cursor: pointer;
        }

        .student-avatar:hover::after {
            content: attr(data-name);
            position: absolute;
            bottom: -30px;
            left: 50%;
            transform: translateX(-50%);
            background: #333;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 1000;
        }

        .student-avatar img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
        }

        .coming-soon {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin: 40px 0;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .coming-soon h3 {
            color: #FFD700;
            margin-bottom: 15px;
            text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
        }

        .coming-soon i {
            font-size: 3rem;
            color: #FFD700;
            margin-bottom: 20px;
            text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
        }

        .coming-soon p {
            color: rgba(255, 255, 255, 0.9);
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../../../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../../../index.html">Trang Chủ</a></li>
                    <li><a href="../../index.html">Lớp Học</a></li>
                    <li><a href="../../../achievements/">Thành Tích</a></li>
                    <li><a href="../../../auth/register.html">Đăng Ký</a></li>
                    <li><a href="../../../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../../../research/">Nghiên Cứu</a></li>
                    <li><a href="../../../auth/">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Class Header -->
    <section class="class-header">
        <div class="container">
            <h1><i class="fas fa-microscope"></i> KHKT - A</h1>
            <p>Hỗ trợ nghiên cứu KHKT và STEM - Chưa có lịch</p>
        </div>
    </section>

    <!-- Access Control Message -->
    <section class="class-info">
        <div class="container">
            <div id="accessMessage" style="display: none;"></div>
            
            <div id="classContent" style="display: none;">
                <!-- Class Information -->
                <div class="info-grid">
                    <div class="info-card">
                        <i class="fas fa-calendar-alt"></i>
                        <h3>Lịch Học</h3>
                        <p>Thứ 2 - Thứ 6<br>20:00 - 21:30</p>
                    </div>
                    <div class="info-card">
                        <i class="fas fa-users"></i>
                        <h3>Học Viên</h3>
                        <p id="studentCount">Đang tải...</p>
                        <div class="students-display">
                            <div id="studentAvatars" class="student-avatars"></div>
                        </div>
                    </div>
                    <div class="info-card">
                        <i class="fas fa-video"></i>
                        <h3>Google Meet</h3>
                        <a href="#" target="_blank" class="meet-link">
                            <i class="fas fa-video"></i> Tham gia lớp học
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Coming Soon Section -->
    <section class="lessons-section" id="lessonsSection" style="display: none;">
        <div class="container">
            <div class="coming-soon">
                <i class="fas fa-flask"></i>
                <h3>Lớp học sắp ra mắt!</h3>
                <p>Nội dung bài học cho lớp KHKT - A đang được chuẩn bị kỹ lưỡng.</p>
                <p>Chương trình sẽ bao gồm hỗ trợ nghiên cứu khoa học kỹ thuật, dự án STEM và phát triển kỹ năng thuyết trình.</p>
                <p>Vui lòng theo dõi thông báo từ giáo viên để biết thêm chi tiết về thời gian bắt đầu.</p>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../../../assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";
        import { getFirestore, doc, getDoc, collection, query, where, getDocs } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        // Class ID for this class
        const CLASS_ID = 'khkt-a';

        // Check if user has access to this class
        async function checkAccess(user) {
            if (!user) {
                return false;
            }

            try {
                const userDoc = await getDoc(doc(db, "users", user.uid));
                if (userDoc.exists()) {
                    const userData = userDoc.data();

                    // Check if user is admin
                    const isAdmin = userData.isAdmin || user.email === '<EMAIL>';

                    // Admin has access to all classes, or user has access to their selected class
                    return isAdmin || userData.courseClass === CLASS_ID;
                }
                return false;
            } catch (error) {
                console.error("Error checking access:", error);
                return false;
            }
        }

        // Load students for this class
        async function loadStudents() {
            try {
                const q = query(
                    collection(db, "users"),
                    where("courseClass", "==", CLASS_ID)
                );
                const querySnapshot = await getDocs(q);

                const students = [];
                querySnapshot.forEach((doc) => {
                    const userData = doc.data();
                    if (userData.fullName && userData.fullName.trim() !== '') {
                        students.push({
                            id: doc.id,
                            userId: doc.id,  // Add userId for consistency
                            name: userData.fullName,
                            avatar: userData.avatar || '../../../../assets/images/avatars/avatar_boy_1.png'
                        });
                    }
                });

                // Update student count
                document.getElementById('studentCount').textContent = `${students.length}/8 học viên`;

                // Display student avatars
                const avatarsContainer = document.getElementById('studentAvatars');
                if (students.length > 0) {
                    avatarsContainer.innerHTML = students.map(student => `
                        <div class="student-avatar" data-name="${student.name}" onclick="viewStudentProfile('${student.userId || student.id}')">
                            <img src="${student.avatar}" alt="${student.name}">
                        </div>
                    `).join('');
                } else {
                    avatarsContainer.innerHTML = '<p style="color: #666; font-style: italic;">Chưa có học viên nào</p>';
                }
            } catch (error) {
                console.error("Error loading students:", error);
                document.getElementById('studentCount').textContent = 'Lỗi tải dữ liệu';
            }
        }

        // View student profile
        window.viewStudentProfile = function(userId) {
            console.log("Navigating to student profile with userId:", userId);
            window.location.href = `../../../auth/student-profile.html?userId=${userId}`;
        };

        // Auth state change listener
        onAuthStateChanged(auth, async (user) => {
            const accessMessage = document.getElementById('accessMessage');
            const classContent = document.getElementById('classContent');
            const lessonsSection = document.getElementById('lessonsSection');

            if (user) {
                const hasAccess = await checkAccess(user);

                if (hasAccess) {
                    accessMessage.style.display = 'none';
                    classContent.style.display = 'block';
                    lessonsSection.style.display = 'block';

                    // Load students
                    await loadStudents();
                } else {
                    accessMessage.innerHTML = `
                        <div class="access-denied">
                            <h3><i class="fas fa-lock"></i> Không có quyền truy cập</h3>
                            <p>Bạn hiện không có quyền truy cập lớp học này!</p>
                            <p>Vui lòng liên hệ giáo viên để được phân lớp hoặc kiểm tra lại thông tin lớp học đã chọn trong tài khoản.</p>
                        </div>
                    `;
                    accessMessage.style.display = 'block';
                    classContent.style.display = 'none';
                    lessonsSection.style.display = 'none';
                }
            } else {
                accessMessage.innerHTML = `
                    <div class="access-denied">
                        <h3><i class="fas fa-sign-in-alt"></i> Vui lòng đăng nhập</h3>
                        <p>Bạn cần đăng nhập để truy cập lớp học này.</p>
                        <a href="../../../auth/" class="meet-link">
                            <i class="fas fa-sign-in-alt"></i> Đăng nhập ngay
                        </a>
                    </div>
                `;
                accessMessage.style.display = 'block';
                classContent.style.display = 'none';
                lessonsSection.style.display = 'none';
            }
        });
    </script>

    <script src="../../../assets/js/script.js"></script>
</body>
</html>
