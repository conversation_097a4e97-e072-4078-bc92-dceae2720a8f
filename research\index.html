<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sự kiện - Vthon</title>
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="../assets/images/favicon.png">
    <link rel="shortcut icon" type="image/png" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="72x72" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="114x114" href="../assets/images/favicon.png">

    <link rel="stylesheet" href="../assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #0f2027 0%, #203a43 25%, #2c5530 50%, #1a4c2e 75%, #0d3d1a 100%);
            color: white;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 255, 120, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(50, 205, 50, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(34, 139, 34, 0.05) 0%, transparent 50%),
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="farmGrid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(50,205,50,0.08)" stroke-width="0.5"/><circle cx="10" cy="10" r="1" fill="rgba(144,238,144,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23farmGrid)"/></svg>');
            opacity: 0.6;
            z-index: -1;
        }

        /* Floating farm elements */
        .farm-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .farm-particle {
            position: absolute;
            font-size: 1.5rem;
            opacity: 0.3;
            animation: farmFloat 15s linear infinite;
        }

        .farm-particle:nth-child(1) { left: 10%; animation-delay: 0s; }
        .farm-particle:nth-child(2) { left: 20%; animation-delay: -3s; }
        .farm-particle:nth-child(3) { left: 30%; animation-delay: -6s; }
        .farm-particle:nth-child(4) { left: 40%; animation-delay: -9s; }
        .farm-particle:nth-child(5) { left: 50%; animation-delay: -12s; }
        .farm-particle:nth-child(6) { left: 60%; animation-delay: -2s; }
        .farm-particle:nth-child(7) { left: 70%; animation-delay: -5s; }
        .farm-particle:nth-child(8) { left: 80%; animation-delay: -8s; }
        .farm-particle:nth-child(9) { left: 90%; animation-delay: -11s; }

        @keyframes farmFloat {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 0.3;
            }
            90% {
                opacity: 0.3;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        /* Modern glassmorphism cards */
        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        /* Farm Code Challenge - Compact Card Design */
        .farm-challenge-card {
            background: linear-gradient(135deg, #2d5016 0%, #4a7c59 50%, #6b8e23 100%);
            border-radius: 25px;
            padding: 30px;
            margin: 60px auto;
            max-width: 900px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border: 2px solid rgba(50, 205, 50, 0.3);
        }

        .farm-challenge-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="farmPattern" width="20" height="20" patternUnits="userSpaceOnUse"><rect width="20" height="20" fill="none"/><circle cx="10" cy="10" r="1" fill="rgba(144,238,144,0.2)"/><path d="M0,10 Q5,5 10,10 T20,10" stroke="rgba(50,205,50,0.1)" stroke-width="0.5" fill="none"/></pattern></defs><rect width="100" height="100" fill="url(%23farmPattern)"/></svg>');
            opacity: 0.6;
            z-index: 1;
        }

        .farm-challenge-content {
            position: relative;
            z-index: 2;
        }

        .farm-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .farm-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: linear-gradient(135deg, #32CD32, #228B22);
            color: white;
            padding: 6px 16px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            margin-bottom: 15px;
            box-shadow: 0 4px 15px rgba(50, 205, 50, 0.4);
        }

        .farm-title {
            font-size: 2.2rem;
            font-weight: 800;
            color: white;
            margin-bottom: 8px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .farm-subtitle {
            color: #b8e6b8;
            font-size: 1rem;
            margin-bottom: 25px;
        }

        .farm-content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            align-items: start;
        }

        .farm-info-section {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .farm-info-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .farm-info-item h4 {
            color: #90EE90;
            font-size: 0.9rem;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .farm-info-item p {
            color: white;
            font-size: 0.85rem;
            line-height: 1.4;
            margin: 0;
        }

        .farm-prizes-section {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .farm-prizes-title {
            color: #FFD700;
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 15px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .farm-prizes-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
        }

        .farm-prize-item {
            text-align: center;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 12px 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .farm-prize-item:hover {
            transform: translateY(-3px);
            background: rgba(255, 255, 255, 0.1);
        }

        .farm-prize-item.top1 { border-color: #FFD700; }
        .farm-prize-item.top2 { border-color: #C0C0C0; }
        .farm-prize-item.top3 { border-color: #CD7F32; }

        .farm-prize-rank {
            font-size: 0.7rem;
            font-weight: 600;
            color: #b8c6db;
            margin-bottom: 5px;
        }

        .farm-prize-badge {
            width: 35px;
            height: 35px;
            margin: 0 auto 8px;
        }

        .farm-prize-badge img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .farm-prize-title {
            font-size: 0.75rem;
            color: white;
            font-weight: 600;
            margin-bottom: 4px;
            line-height: 1.2;
        }

        .farm-prize-amount {
            font-size: 0.8rem;
            color: #32CD32;
            font-weight: 700;
        }

        .farm-actions {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 25px;
        }

        .farm-btn {
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .farm-btn-primary {
            background: linear-gradient(135deg, #32CD32, #228B22);
            color: white;
            box-shadow: 0 4px 15px rgba(50, 205, 50, 0.3);
        }

        .farm-btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(50, 205, 50, 0.4);
        }

        .farm-btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .farm-btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        /* Summer Camp Card - Compact Design */
        .summer-camp-card {
            background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 25%, #ff6b35 50%, #f7931e 75%, #ffd93d 100%);
            border-radius: 25px;
            padding: 30px;
            margin: 60px auto;
            max-width: 900px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border: 2px solid rgba(255, 215, 0, 0.4);
        }

        .summer-camp-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="summerPattern" width="25" height="25" patternUnits="userSpaceOnUse"><rect width="25" height="25" fill="none"/><circle cx="12.5" cy="12.5" r="2" fill="rgba(255,255,255,0.1)"/><path d="M12.5,5 L15,10 L20,10 L16,14 L17.5,19 L12.5,16 L7.5,19 L9,14 L5,10 L10,10 Z" fill="rgba(255,215,0,0.2)"/></pattern></defs><rect width="100" height="100" fill="url(%23summerPattern)"/></svg>');
            opacity: 0.6;
            z-index: 1;
        }

        .summer-camp-content {
            position: relative;
            z-index: 2;
        }

        .summer-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .summer-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: linear-gradient(135deg, #ff4757, #ff3742);
            color: white;
            padding: 6px 16px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            margin-bottom: 15px;
            box-shadow: 0 4px 15px rgba(255, 71, 87, 0.4);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .summer-title {
            font-size: 2.2rem;
            font-weight: 800;
            color: white;
            margin-bottom: 8px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .summer-subtitle {
            color: #fff3cd;
            font-size: 1rem;
            margin-bottom: 25px;
        }

        .summer-content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            align-items: start;
        }

        .summer-info-section {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .summer-info-item {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            padding: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .summer-info-item h4 {
            color: #fff3cd;
            font-size: 0.9rem;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .summer-info-item p {
            color: white;
            font-size: 0.85rem;
            line-height: 1.4;
            margin: 0;
        }

        .summer-prizes-section {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .summer-prizes-title {
            color: #ffd93d;
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 15px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .summer-prizes-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
        }

        .summer-prize-item {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 12px 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .summer-prize-item:hover {
            transform: translateY(-3px);
            background: rgba(255, 255, 255, 0.2);
        }

        .summer-prize-item.top1 { border-color: #ffd93d; }
        .summer-prize-item.top2 { border-color: #ff8e53; }
        .summer-prize-item.top3 { border-color: #ff6b35; }

        .summer-prize-rank {
            font-size: 0.7rem;
            font-weight: 600;
            color: #fff3cd;
            margin-bottom: 5px;
        }

        .summer-prize-badge {
            width: 35px;
            height: 35px;
            margin: 0 auto 8px;
        }

        .summer-prize-badge img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .summer-prize-title {
            font-size: 0.75rem;
            color: white;
            font-weight: 600;
            margin-bottom: 4px;
            line-height: 1.2;
        }

        .summer-prize-amount {
            font-size: 0.8rem;
            color: #ffd93d;
            font-weight: 700;
        }

        .summer-actions {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 25px;
        }

        .summer-btn {
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .summer-btn-primary {
            background: linear-gradient(135deg, #ff4757, #ff3742);
            color: white;
            box-shadow: 0 4px 15px rgba(255, 71, 87, 0.3);
        }

        .summer-btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 71, 87, 0.4);
        }

        .summer-btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.4);
        }

        .summer-btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        /* Xuân Scratch Game Festival - Tet Theme */
        .tet-festival-card {
            background: linear-gradient(135deg, #8b0000 0%, #dc143c 25%, #b8860b 50%, #cd853f 75%, #d2691e 100%);
            border-radius: 25px;
            padding: 30px;
            margin: 60px auto;
            max-width: 900px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border: 2px solid rgba(255, 215, 0, 0.5);
        }

        .tet-festival-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="tetPattern" width="30" height="30" patternUnits="userSpaceOnUse"><rect width="30" height="30" fill="none"/><circle cx="15" cy="15" r="2" fill="rgba(255,215,0,0.15)"/><path d="M15,5 L18,12 L25,12 L20,17 L22,24 L15,20 L8,24 L10,17 L5,12 L12,12 Z" fill="rgba(255,69,0,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23tetPattern)"/></svg>');
            opacity: 0.4;
            z-index: 1;
        }

        .tet-festival-content {
            position: relative;
            z-index: 2;
        }

        .tet-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .tet-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: linear-gradient(135deg, #dc143c, #b91c3c);
            color: white;
            padding: 6px 16px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            margin-bottom: 15px;
            box-shadow: 0 4px 15px rgba(220, 20, 60, 0.4);
            animation: tetGlow 3s ease-in-out infinite alternate;
        }

        @keyframes tetGlow {
            0% { box-shadow: 0 4px 15px rgba(220, 20, 60, 0.4); }
            100% { box-shadow: 0 6px 25px rgba(255, 215, 0, 0.6); }
        }

        .tet-title {
            font-size: 2.2rem;
            font-weight: 800;
            color: #ffffff;
            margin-bottom: 8px;
            text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8), 1px 1px 3px rgba(0, 0, 0, 0.9);
        }

        .tet-subtitle {
            color: #ffffff;
            font-size: 1rem;
            margin-bottom: 25px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
        }

        .tet-content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            align-items: start;
        }

        .tet-info-section {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .tet-info-item {
            background: rgba(0, 0, 0, 0.4);
            border-radius: 12px;
            padding: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 215, 0, 0.4);
        }

        .tet-info-item h4 {
            color: #ffd700;
            font-size: 0.9rem;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 700;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }

        .tet-info-item p {
            color: #ffffff;
            font-size: 0.85rem;
            line-height: 1.4;
            margin: 0;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
        }

        .tet-prizes-section {
            background: rgba(0, 0, 0, 0.5);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 215, 0, 0.4);
        }

        .tet-prizes-title {
            color: #ffd700;
            font-size: 1.1rem;
            font-weight: 700;
            margin-bottom: 15px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
        }

        .tet-prizes-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
        }

        .tet-prize-item {
            text-align: center;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 12px 8px;
            border: 1px solid rgba(255, 215, 0, 0.3);
            transition: all 0.3s ease;
        }

        .tet-prize-item:hover {
            transform: translateY(-3px);
            background: rgba(0, 0, 0, 0.4);
            border-color: rgba(255, 215, 0, 0.6);
        }

        .tet-prize-item.top1 { border-color: #ffd700; }
        .tet-prize-item.top2 { border-color: #ff8c00; }
        .tet-prize-item.top3 { border-color: #ff6347; }

        .tet-prize-rank {
            font-size: 0.7rem;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 5px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }

        .tet-prize-badge {
            width: 35px;
            height: 35px;
            margin: 0 auto 8px;
        }

        .tet-prize-badge img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .tet-prize-title {
            font-size: 0.75rem;
            color: #ffffff;
            font-weight: 700;
            margin-bottom: 4px;
            line-height: 1.2;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }

        .tet-prize-amount {
            font-size: 0.8rem;
            color: #ffd700;
            font-weight: 800;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }

        .tet-actions {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 25px;
        }

        .tet-btn {
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .tet-btn-primary {
            background: linear-gradient(135deg, #dc143c, #b91c3c);
            color: white;
            box-shadow: 0 4px 15px rgba(220, 20, 60, 0.3);
        }

        .tet-btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(220, 20, 60, 0.4);
        }

        .tet-btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 215, 0, 0.4);
        }

        .tet-btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .farm-challenge-card, .summer-camp-card, .tet-festival-card {
                margin: 40px 20px;
                padding: 25px 20px;
            }

            .farm-content-grid, .summer-content-grid, .tet-content-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .farm-title, .summer-title, .tet-title {
                font-size: 1.8rem;
            }

            .farm-actions, .summer-actions, .tet-actions {
                flex-direction: column;
                align-items: center;
            }

            .farm-btn, .summer-btn, .tet-btn {
                width: 200px;
                justify-content: center;
            }
        }

        .page-header {
            text-align: center;
            margin: 120px 0 50px;
            position: relative;
            z-index: 2;
        }

        .page-header h1 {
            font-size: 3.5rem;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #FFD700, #FFA500, #FF8C00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 800;
            text-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
        }

        .page-header p {
            color: #b8c6db;
            font-size: 1.3rem;
            max-width: 700px;
            margin: 0 auto;
        }

        .events-container {
            padding: 20px 0 50px;
            position: relative;
            z-index: 2;
        }

    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../index.html">Trang Chủ</a></li>
                    <li><a href="../classes/">Lớp Học</a></li>
                    <li><a href="../achievements/">Thành Tích</a></li>
                    <li><a href="../auth/register.html">Đăng Ký</a></li>
                    <li><a href="../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="index.html" class="active">Sự kiện</a></li>
                    <li><a href="../auth/">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Farm Particles -->
    <div class="farm-particles">
        <div class="farm-particle">🌱</div>
        <div class="farm-particle">🌾</div>
        <div class="farm-particle">🚁</div>
        <div class="farm-particle">💧</div>
        <div class="farm-particle">🌿</div>
        <div class="farm-particle">🌽</div>
        <div class="farm-particle">🥕</div>
        <div class="farm-particle">🍃</div>
        <div class="farm-particle">⚡</div>
    </div>

    <!-- Events Section -->
    <section class="code-camp-event" id="events">
        <div class="container">
            <div class="page-header" data-aos="fade-up">
                <h1>Sự kiện & Cuộc thi</h1>
                <p>Tham gia các sự kiện và cuộc thi lập trình hấp dẫn tại Vthon Academy</p>
            </div>



        <!-- Code Camp Hè 2025 - Compact Card -->
        <div class="summer-camp-card" data-aos="fade-up">
            <div class="summer-camp-content">
                <div class="summer-header">
                    <div class="summer-badge">
                        <i class="fas fa-fire"></i>
                        <span>ĐANG DIỄN RA</span>
                    </div>
                    <h2 class="summer-title">🔥 Code Camp Hè 2025</h2>
                    <p class="summer-subtitle">Cuộc thi lập trình hè dành cho học viên Python</p>
                </div>

                <div class="summer-content-grid">
                    <div class="summer-info-section">
                        <div class="summer-info-item">
                            <h4><i class="fas fa-calendar-alt"></i> Thời Gian</h4>
                            <p><strong>14/06 - 01/08/2025</strong> - Đang diễn ra sôi nổi</p>
                        </div>

                        <div class="summer-info-item">
                            <h4><i class="fas fa-users"></i> Đối Tượng</h4>
                            <p>Tất cả học viên đang theo học các lớp Python</p>
                        </div>

                        <div class="summer-info-item">
                            <h4><i class="fas fa-chart-line"></i> Cách Thức</h4>
                            <p>Điểm số trên Bảng Xếp Hạng quyết định thứ hạng</p>
                        </div>
                    </div>

                    <div class="summer-prizes-section">
                        <h3 class="summer-prizes-title">
                            <i class="fas fa-gift"></i>
                            Giải Thưởng
                        </h3>

                        <div class="summer-prizes-grid">
                            <div class="summer-prize-item top1">
                                <div class="summer-prize-rank">TOP 1</div>
                                <div class="summer-prize-badge">
                                    <img src="../assets/images/badges/top1_cuoc_thi_moi.png" alt="Top 1 Badge">
                                </div>
                                <div class="summer-prize-title">Huy Hiệu Độc Quyền</div>
                                <div class="summer-prize-amount">150,000 VNĐ</div>
                            </div>

                            <div class="summer-prize-item top2">
                                <div class="summer-prize-rank">TOP 2</div>
                                <div class="summer-prize-badge">
                                    <img src="../assets/images/badges/top2_cuoc_thi_moi.png" alt="Top 2 Badge">
                                </div>
                                <div class="summer-prize-title">Huy Hiệu Độc Quyền</div>
                                <div class="summer-prize-amount">100,000 VNĐ</div>
                            </div>

                            <div class="summer-prize-item top3">
                                <div class="summer-prize-rank">TOP 3</div>
                                <div class="summer-prize-badge">
                                    <img src="../assets/images/badges/top3_cuoc_thi_moi.png" alt="Top 3 Badge">
                                </div>
                                <div class="summer-prize-title">Huy Hiệu Độc Quyền</div>
                                <div class="summer-prize-amount">50,000 VNĐ</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="summer-actions">
                    <a href="../rankings/" class="summer-btn summer-btn-primary">
                        <i class="fas fa-chart-line"></i>
                        Xem Bảng Xếp Hạng
                    </a>
                    <a href="../classes/" class="summer-btn summer-btn-secondary">
                        <i class="fas fa-code"></i>
                        Tham Gia Ngay
                    </a>
                </div>
            </div>
        </div>

        <!-- Farm Code Challenge Event -->
        <div class="farm-challenge-card" data-aos="fade-up">
            <div class="farm-challenge-content">
                <div class="farm-header">
                    <div class="farm-badge">
                        <i class="fas fa-seedling"></i>
                        <span>SẮP DIỄN RA</span>
                    </div>
                    <h2 class="farm-title">🌾 Farm Code Challenge 2026</h2>
                    <p class="farm-subtitle">Cuộc thi lập trình điều khiển drone nông nghiệp thông minh</p>
                </div>

                <div class="farm-content-grid">
                    <div class="farm-info-section">
                        <div class="farm-info-item">
                            <h4><i class="fas fa-calendar-alt"></i> Thời Gian</h4>
                            <p><strong>Năm 2026</strong> - Thời gian cụ thể sẽ được thông báo sớm</p>
                        </div>

                        <div class="farm-info-item">
                            <h4><i class="fas fa-puzzle-piece"></i> Hình Thức</h4>
                            <p>Kéo thả trực quan để tạo thuật toán điều khiển drone</p>
                        </div>

                        <div class="farm-info-item">
                            <h4><i class="fas fa-brain"></i> Kỹ Năng</h4>
                            <p>Tư duy logic, điều kiện và vòng lặp cơ bản</p>
                        </div>
                    </div>

                    <div class="farm-prizes-section">
                        <h3 class="farm-prizes-title">
                            <i class="fas fa-trophy"></i>
                            Giải Thưởng
                        </h3>

                        <div class="farm-prizes-grid">
                            <div class="farm-prize-item top1">
                                <div class="farm-prize-rank">TOP 1</div>
                                <div class="farm-prize-badge">
                                    <img src="../assets/images/badges/giainhat.png" alt="Bậc Thầy Nông Trại">
                                </div>
                                <div class="farm-prize-title">Bậc Thầy Nông Trại</div>
                                <div class="farm-prize-amount">200,000 VNĐ</div>
                            </div>

                            <div class="farm-prize-item top2">
                                <div class="farm-prize-rank">TOP 2</div>
                                <div class="farm-prize-badge">
                                    <img src="../assets/images/badges/giainhi.png" alt="Chuyên Gia Tự Động Hóa">
                                </div>
                                <div class="farm-prize-title">Chuyên Gia Tự Động Hóa</div>
                                <div class="farm-prize-amount">150,000 VNĐ</div>
                            </div>

                            <div class="farm-prize-item top3">
                                <div class="farm-prize-rank">TOP 3</div>
                                <div class="farm-prize-badge">
                                    <img src="../assets/images/badges/giaiba.png" alt="Lập Trình Viên Canh Tác">
                                </div>
                                <div class="farm-prize-title">Lập Trình Viên Canh Tác</div>
                                <div class="farm-prize-amount">100,000 VNĐ</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="farm-actions">
                    <a href="../tests/farm-code-demo.html" class="farm-btn farm-btn-primary">
                        <i class="fas fa-seedling"></i>
                        Xem Demo
                    </a>
                    <a href="../auth/register.html" class="farm-btn farm-btn-secondary">
                        <i class="fas fa-user-plus"></i>
                        Đăng Ký Tham Gia
                    </a>
                </div>
            </div>
        </div>

        <!-- Xuân Scratch Game Festival Event -->
        <div class="tet-festival-card" data-aos="fade-up">
            <div class="tet-festival-content">
                <div class="tet-header">
                    <div class="tet-badge">
                        <i class="fas fa-calendar-plus"></i>
                        <span>SẮP DIỄN RA</span>
                    </div>
                    <h2 class="tet-title">🧧 Xuân Scratch - Game Festival</h2>
                    <p class="tet-subtitle">Lễ hội game Scratch chào đón Tết Nguyên Đán</p>
                </div>

                <div class="tet-content-grid">
                    <div class="tet-info-section">
                        <div class="tet-info-item">
                            <h4><i class="fas fa-calendar-alt"></i> Thời Gian</h4>
                            <p><strong>Mùa Xuân 2026</strong> - Cùng đón Tết với những game sáng tạo</p>
                        </div>

                        <div class="tet-info-item">
                            <h4><i class="fas fa-gamepad"></i> Đối Tượng</h4>
                            <p>Học viên đang theo học các lớp Scratch tại Vthon Academy</p>
                        </div>

                        <div class="tet-info-item">
                            <h4><i class="fas fa-trophy"></i> Hình Thức</h4>
                            <p>Tạo game Scratch với chủ đề Tết và truyền thống Việt Nam</p>
                        </div>
                    </div>

                    <div class="tet-prizes-section">
                        <h3 class="tet-prizes-title">
                            <i class="fas fa-gift"></i>
                            Giải Thưởng
                        </h3>

                        <div class="tet-prizes-grid">
                            <div class="tet-prize-item top1">
                                <div class="tet-prize-rank">TOP 1</div>
                                <div class="tet-prize-badge">
                                    <img src="../assets/images/badges/giainhatphim.png" alt="Nhà Phát Triển Game Xuất Sắc">
                                </div>
                                <div class="tet-prize-title">Nhà Phát Triển Game Xuất Sắc</div>
                                <div class="tet-prize-amount">200,000 VNĐ</div>
                            </div>

                            <div class="tet-prize-item top2">
                                <div class="tet-prize-rank">TOP 2</div>
                                <div class="tet-prize-badge">
                                    <img src="../assets/images/badges/giainhiphim.png" alt="Lập Trình Viên Sáng Tạo">
                                </div>
                                <div class="tet-prize-title">Lập Trình Viên Sáng Tạo</div>
                                <div class="tet-prize-amount">150,000 VNĐ</div>
                            </div>

                            <div class="tet-prize-item top3">
                                <div class="tet-prize-rank">TOP 3</div>
                                <div class="tet-prize-badge">
                                    <img src="../assets/images/badges/giaibaphim.png" alt="Nghệ Sĩ Game Trẻ">
                                </div>
                                <div class="tet-prize-title">Nghệ Sĩ Game Trẻ</div>
                                <div class="tet-prize-amount">100,000 VNĐ</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tet-actions">
                    <a href="../classes/" class="tet-btn tet-btn-primary">
                        <i class="fas fa-gamepad"></i>
                        Xem Lớp Học
                    </a>
                    <a href="../auth/register.html" class="tet-btn tet-btn-secondary">
                        <i class="fas fa-user-plus"></i>
                        Đăng Ký Ngay
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- AOS Animation Library -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        AOS.init({
            duration: 1000,
            once: true,
            offset: 100
        });
    </script>

    <script src="../assets/js/script.js"></script>
</body>
</html>
