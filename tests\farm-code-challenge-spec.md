# 🌾 Farm Code Challenge 2025 - Đặc tả chi tiết

## 📋 Tổng quan sự kiện

**Farm Code Challenge 2025** là cuộc thi lập trình độc đáo dành cho học viên Python tại Vthon Academy, l<PERSON>y cảm hứng từ game "The Farmer Was Replaced". Học viên sẽ sử dụng giao diện kéo thả trực quan để tạo thuật toán điều khiển drone nông nghiệp thông minh.

## 🎯 Mục tiêu giáo dục

### Kỹ năng lập trình:
- **Logic Programming**: Hiểu về điều kiện, vòng lặp, sequence
- **Algorithm Design**: Tối ưu hóa quy trình và resource management
- **Visual Programming**: Tiếp cận programming qua drag-and-drop
- **Problem Solving**: Phân tích và giải quyết vấn đề tự động hóa

### <PERSON><PERSON><PERSON> thức thực tế:
- **IoT & Automation**: Ứng dụng trong nông nghiệp thông minh
- **Resource Optimization**: <PERSON>u<PERSON>n lý tài nguyên hiệu quả
- **Strategy Planning**: Lập kế hoạch dài hạn và ngắn hạn

## 🎮 Cơ chế gameplay

### Grid System:
- **Kích thước**: 8x8 hoặc 10x10 tùy level
- **Terrain**: Đất trống, đã gieo, đang phát triển, sẵn sàng thu hoạch
- **Weather**: Nắng, mưa, bão ảnh hưởng đến strategy

### Drone Control:
- **Movement**: 4 hướng cơ bản + diagonal (advanced)
- **Actions**: Plant, Water, Harvest, Scan, Wait
- **Energy System**: Mỗi action tiêu tốn energy khác nhau

### Resource Management:
- **Seeds**: Giới hạn số lượng hạt giống
- **Water**: Cần thiết cho growth, bị ảnh hưởng bởi weather
- **Energy**: Pin drone, tự động regenerate theo thời gian
- **Time**: Giới hạn thời gian hoặc unlimited tùy mode

## 🧩 Visual Programming Interface

### Block Categories:

#### 🔍 Conditions (Điều kiện):
- `if can_plant()` - Kiểm tra có thể gieo hạt
- `if can_water()` - Kiểm tra có thể tưới nước
- `if can_harvest()` - Kiểm tra có thể thu hoạch
- `if energy > X` - Kiểm tra năng lượng
- `if weather == "rain"` - Kiểm tra thời tiết
- `if crop_ready()` - Kiểm tra cây chín

#### ⚡ Actions (Hành động):
- `plant_seed()` - Gieo hạt
- `water_crop()` - Tưới nước
- `harvest_crop()` - Thu hoạch
- `move_to(x, y)` - Di chuyển đến vị trí
- `move_random()` - Di chuyển ngẫu nhiên
- `scan_area()` - Quét khu vực
- `wait(seconds)` - Chờ đợi

#### 🔄 Loops (Vòng lặp):
- `for i in range(N)` - Lặp N lần
- `while energy > X` - Lặp khi còn năng lượng
- `while has_seeds()` - Lặp khi còn hạt
- `for each cell in grid` - Duyệt toàn bộ grid

#### 🧮 Advanced (Nâng cao):
- `calculate_distance()` - Tính khoảng cách
- `find_nearest_ready()` - Tìm cây gần nhất sẵn sàng
- `optimize_path()` - Tối ưu đường đi
- `predict_weather()` - Dự đoán thời tiết

## 🏆 Hệ thống chấm điểm

### Scoring Formula:
```
Total Score = (Harvest Points × Efficiency Bonus) + Strategy Bonus - Resource Penalty
```

### Chi tiết điểm:
- **Harvest Points**: 50 điểm/cây thu hoạch thành công
- **Efficiency Bonus**: Tỷ lệ thu hoạch/thời gian (max 2.0x)
- **Strategy Bonus**: Đa dạng hóa cây trồng, dự đoán thời tiết
- **Resource Penalty**: Lãng phí nước, năng lượng

### Leaderboard:
- **Real-time ranking** cập nhật liên tục
- **Multiple attempts** - lấy điểm cao nhất
- **Replay system** - xem lại các run hay nhất

## 🎖️ Giải thưởng & Danh hiệu

### 🥇 Nhất (Top 1): 200,000 VNĐ
- **Huy hiệu**: "Bậc Thầy Nông Trại" 
- **Danh hiệu**: "Drone Commander"

### 🥈 Nhì (Top 2): 150,000 VNĐ  
- **Huy hiệu**: "Chuyên Gia Tự Động Hóa"
- **Danh hiệu**: "Smart Farmer"

### 🥉 Ba (Top 3): 100,000 VNĐ
- **Huy hiệu**: "Lập Trình Viên Canh Tác"
- **Danh hiệu**: "Code Cultivator"

### 🌱 Khuyến khích (Top 4-10):
- **Huy hiệu**: "Nông Dân Tương Lai"
- **Danh hiệu**: "Rising Farmer"

## 🔮 Roadmap phát triển

### Phase 1 - MVP (2 tuần):
- [x] Visual drag-and-drop interface
- [x] Basic grid system và drone control
- [x] Simple scoring system
- [ ] User authentication integration
- [ ] Basic leaderboard

### Phase 2 - Enhanced (1 tháng):
- [ ] Advanced blocks (pathfinding, optimization)
- [ ] Multiple crop types với growth rates khác nhau
- [ ] Weather system với prediction
- [ ] Replay system và code sharing
- [ ] Mobile responsive optimization

### Phase 3 - Advanced (2 tháng):
- [ ] Multiplayer mode - cạnh tranh real-time
- [ ] AI opponent với different difficulty levels
- [ ] Custom block creation cho advanced users
- [ ] Integration với Python code editor
- [ ] Tournament system với brackets

### Phase 4 - Professional (3 tháng):
- [ ] 3D visualization với Three.js
- [ ] Machine Learning integration
- [ ] Real IoT device simulation
- [ ] API cho external integrations
- [ ] Educational curriculum integration

## 🛠️ Technical Stack

### Frontend:
- **HTML5/CSS3/JavaScript** - Core interface
- **Drag & Drop API** - Block manipulation
- **Canvas/WebGL** - Game rendering
- **WebSocket** - Real-time updates

### Backend:
- **Firebase** - Authentication & database
- **Python** - Code execution engine
- **Node.js** - Real-time server
- **Docker** - Sandboxed code execution

### Security:
- **Code sandboxing** - An toàn khi execute user code
- **Rate limiting** - Chống spam submissions
- **Input validation** - Kiểm tra blocks hợp lệ

## 📚 Educational Integration

### Curriculum Alignment:
- **Scratch Foundation** - Visual programming concepts
- **Python Transition** - Bridge từ visual sang text
- **Algorithm Design** - Problem solving methodology
- **Real-world Applications** - IoT và automation

### Assessment Methods:
- **Peer Review** - Học viên review code của nhau
- **Code Explanation** - Giải thích logic trong video
- **Optimization Challenge** - Cải thiện solution existing
- **Creative Mode** - Tự do sáng tạo scenarios

## 🎨 UI/UX Design Principles

### Accessibility:
- **Color-blind friendly** palette
- **Large touch targets** cho mobile
- **Clear visual hierarchy**
- **Intuitive drag & drop** feedback

### Gamification:
- **Progress bars** cho từng action
- **Achievement system** với badges
- **Level progression** từ easy đến expert
- **Social features** - share và like solutions

---

*Tài liệu này sẽ được cập nhật liên tục theo feedback từ học viên và giáo viên.*
