<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> - Vthon Academy</title>
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="../assets/images/favicon.png">
    <link rel="shortcut icon" type="image/png" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="72x72" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="114x114" href="../assets/images/favicon.png">

    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Dark Background with Starfield Effect */
        body {
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        /* Starfield Background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(2px 2px at 20px 30px, #fff, transparent),
                radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
                radial-gradient(1px 1px at 90px 40px, #fff, transparent),
                radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
                radial-gradient(2px 2px at 160px 30px, #fff, transparent);
            background-repeat: repeat;
            background-size: 200px 100px;
            animation: sparkle 3s linear infinite;
            opacity: 0.3;
            z-index: -1;
        }

        /* Shooting Stars */
        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                linear-gradient(90deg, transparent, rgba(255,255,255,0.8), transparent),
                linear-gradient(90deg, transparent, rgba(255,215,0,0.6), transparent),
                linear-gradient(90deg, transparent, rgba(255,140,0,0.4), transparent);
            background-size: 200px 2px, 150px 1px, 100px 1px;
            background-position: -200px 20%, -150px 40%, -100px 70%;
            background-repeat: no-repeat;
            animation: shootingStars 4s linear infinite;
            pointer-events: none;
            z-index: -1;
        }

        @keyframes sparkle {
            0% { transform: translateY(0px); }
            100% { transform: translateY(-100px); }
        }

        @keyframes shootingStars {
            0% {
                background-position: -200px 20%, -150px 40%, -100px 70%;
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                background-position: 100vw 20%, 100vw 40%, 100vw 70%;
                opacity: 0;
            }
        }

        /* Profile Page Styles */
        .profile-container {
            max-width: 1200px;
            margin: 120px auto 0;
            padding: 20px;
            min-height: calc(100vh - 200px);
            position: relative;
            z-index: 1;
        }

        .profile-header {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            gap: 30px;
            animation: fadeInUp 0.8s ease-out forwards;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .profile-avatar {
            position: relative;
            width: 120px;
            height: 120px;
        }

        .profile-avatar img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid #FFD700;
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
        }

        .profile-avatar .edit-overlay {
            position: absolute;
            bottom: 0;
            right: 0;
            background: linear-gradient(135deg, #FFD700, #FF8C00);
            color: #333;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .profile-avatar .edit-overlay:hover {
            background: linear-gradient(135deg, #FF8C00, #FFD700);
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
        }

        .profile-info h1 {
            margin: 0 0 10px 0;
            color: white;
            font-size: 2.5rem;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
            background: linear-gradient(135deg, #ffd700, #ff8c00, #ff6b35);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .profile-info .email {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.1rem;
            margin-bottom: 15px;
        }

        .profile-stats {
            display: flex;
            gap: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 10px 20px;
            background: linear-gradient(135deg, #FFD700 0%, #FF8C00 100%);
            color: #333;
            border-radius: 15px;
            min-width: 80px;
            box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            display: block;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .profile-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .profile-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            animation: fadeInUp 0.8s ease-out forwards;
        }

        .profile-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.4);
            border-color: rgba(255, 215, 0, 0.3);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid rgba(255, 255, 255, 0.1);
        }

        .card-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #FFD700 0%, #FF8C00 100%);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            font-size: 1.2rem;
            box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: white;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        .inbox-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            border-radius: 12px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }

        .inbox-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
            border-left-color: #FFD700;
        }

        .inbox-item.unread {
            background: rgba(255, 215, 0, 0.1);
            border-left-color: #FFD700;
        }

        .inbox-item.unread .message-title {
            font-weight: 600;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #FFD700 0%, #FF8C00 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            font-weight: 600;
            box-shadow: 0 2px 10px rgba(255, 215, 0, 0.3);
        }

        .message-content {
            flex: 1;
        }

        .message-title {
            font-size: 1rem;
            color: white;
            margin: 0 0 5px 0;
        }

        .message-preview {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
            margin: 0;
        }

        .message-time {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.5);
        }

        .message-actions {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .delete-message-btn {
            background: none;
            border: none;
            color: #e53e3e;
            cursor: pointer;
            padding: 5px;
            border-radius: 4px;
            transition: all 0.2s ease;
            opacity: 0.7;
        }

        .delete-message-btn:hover {
            background: #fed7d7;
            opacity: 1;
            transform: scale(1.1);
        }

        .inbox-item {
            position: relative;
        }

        .inbox-item:hover .delete-message-btn {
            opacity: 1;
        }

        /* Message Modal Styles */
        .message-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            animation: fadeIn 0.3s ease;
        }

        .message-modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            border-radius: 15px;
            padding: 0;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            animation: slideIn 0.3s ease;
        }

        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 25px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 1.3rem;
        }

        .modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: background 0.3s ease;
        }

        .modal-close:hover {
            background: rgba(255,255,255,0.2);
        }

        .modal-body {
            padding: 25px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .modal-message-info {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e2e8f0;
        }

        .modal-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 1.2rem;
        }

        .modal-sender-info h4 {
            margin: 0;
            color: #2d3748;
            font-size: 1.1rem;
        }

        .modal-sender-info p {
            margin: 5px 0 0 0;
            color: #718096;
            font-size: 0.9rem;
        }

        .modal-message-content {
            line-height: 1.6;
            color: #4a5568;
            font-size: 1rem;
            white-space: pre-wrap;
        }

        /* Pagination Styles */
        .pagination-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 20px;
            padding: 15px 0;
        }

        .pagination-info {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }

        .pagination-controls {
            display: flex;
            gap: 10px;
        }

        .pagination-btn {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 215, 0, 0.3);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
            backdrop-filter: blur(10px);
        }

        .pagination-btn:hover:not(:disabled) {
            background: linear-gradient(135deg, #FFD700, #FF8C00);
            color: #333;
            border-color: #FFD700;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from { transform: translateY(-50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .modal-content {
                width: 95%;
                max-height: 90vh;
            }

            .modal-header {
                padding: 15px 20px;
            }

            .modal-body {
                padding: 20px;
            }

            .pagination-container {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }

            .pagination-controls {
                justify-content: center;
            }
        }

        .achievements-grid {
            width: 100%;
        }

        .badges-section h4 {
            color: white;
            margin: 0 0 20px 0;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.1rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        .badges-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .badge-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 215, 0, 0.2);
            transition: all 0.3s ease;
            position: relative;
        }

        .badge-item.earned {
            border-color: #ffd700;
            background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
        }

        .badge-item.unearned {
            opacity: 0.6;
            background: rgba(255, 255, 255, 0.02);
        }

        .badge-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .badge-image {
            position: relative;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            overflow: hidden;
            flex-shrink: 0;
        }

        .badge-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .badge-image.legendary {
            border: 3px solid #ffd700;
            box-shadow: 0 0 15px rgba(255, 215, 0, 0.5);
        }

        .badge-image.epic {
            border: 3px solid #9f7aea;
            box-shadow: 0 0 15px rgba(159, 122, 234, 0.5);
        }

        .badge-image.rare {
            border: 3px solid #4299e1;
            box-shadow: 0 0 15px rgba(66, 153, 225, 0.5);
        }

        .badge-lock {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }

        .badge-info {
            flex: 1;
        }

        .badge-name {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .badge-description {
            font-size: 0.85rem;
            color: #718096;
            line-height: 1.4;
        }

        .empty-achievements {
            text-align: center;
            padding: 40px 20px;
            color: rgba(255, 255, 255, 0.7);
        }

        .empty-achievements .empty-icon {
            font-size: 3rem;
            color: rgba(255, 215, 0, 0.5);
            margin-bottom: 20px;
        }

        .empty-achievements h4 {
            color: white;
            margin: 0 0 10px 0;
            font-size: 1.2rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .action-btn {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 15px 20px;
            background: linear-gradient(135deg, #FFD700 0%, #FF8C00 100%);
            color: #333;
            text-decoration: none;
            border-radius: 12px;
            transition: all 0.3s ease;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
        }

        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(255, 215, 0, 0.5);
            color: #333;
            background: linear-gradient(135deg, #FF8C00 0%, #FFD700 100%);
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #e53e3e;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        /* Empty Inbox Styles */
        .empty-inbox {
            text-align: center;
            padding: 40px 20px;
            color: rgba(255, 255, 255, 0.7);
        }

        .empty-icon {
            font-size: 3rem;
            color: rgba(255, 215, 0, 0.5);
            margin-bottom: 20px;
        }

        .empty-inbox h4 {
            color: white;
            margin: 0 0 10px 0;
            font-size: 1.2rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        .empty-inbox p {
            margin: 0;
            line-height: 1.6;
            max-width: 300px;
            margin: 0 auto;
        }

        @media (max-width: 768px) {
            .profile-header {
                flex-direction: column;
                text-align: center;
            }

            .profile-content {
                grid-template-columns: 1fr;
            }

            .profile-stats {
                justify-content: center;
            }

            .footer-content {
                grid-template-columns: 1fr;
                gap: 30px;
            }

            .nav-menu {
                flex-wrap: wrap;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../index.html">Trang Chủ</a></li>
                    <li><a href="../classes/">Lớp Học</a></li>
                    <li><a href="../achievements/">Thành Tích</a></li>
                    <li><a href="register.html">Đăng Ký</a></li>
                    <li><a href="../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../research/">Sự kiện</a></li>
                    <li><a href="index.html" class="active">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Profile Container -->
    <div class="profile-container">
        <!-- Profile Header -->
        <div class="profile-header">
            <div class="profile-avatar">
                <img id="profileAvatarImg" src="../assets/images/avatars/avatar_boy_1.png" alt="Avatar">
                <div class="edit-overlay" onclick="openAvatarModal()">
                    <i class="fas fa-camera"></i>
                </div>
            </div>
            <div class="profile-info">
                <h1 id="profileName">Lê Quang Vinh</h1>
                <div class="email" id="profileEmail"><EMAIL></div>
                <div class="profile-stats">
                    <div class="stat-item">
                        <span class="stat-number" id="statClasses">0</span>
                        <span class="stat-label">Lớp học</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" id="statAssignments">2</span>
                        <span class="stat-label">Bài tập</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" id="statRank">-</span>
                        <span class="stat-label">Xếp hạng</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" id="statAchievements">0</span>
                        <span class="stat-label">Huy hiệu</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Content -->
        <div class="profile-content">
            <!-- Inbox Card -->
            <div class="profile-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-inbox"></i>
                    </div>
                    <h3 class="card-title">Hộp Thư</h3>
                    <div style="margin-left: auto; position: relative;">
                        <span id="inboxNotificationBadge" class="notification-badge" style="display: none;"></span>
                    </div>
                </div>
                <div id="inboxContent">
                    <!-- Inbox items will be populated by JavaScript -->
                </div>
                <div class="pagination-container" id="inboxPagination" style="display: none;">
                    <div class="pagination-info" id="inboxPaginationInfo"></div>
                    <div class="pagination-controls">
                        <button class="pagination-btn" id="inboxPrevBtn" onclick="changeInboxPage(-1)">
                            <i class="fas fa-chevron-left"></i> Trước
                        </button>
                        <button class="pagination-btn" id="inboxNextBtn" onclick="changeInboxPage(1)">
                            Sau <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Achievements Card -->
            <div class="profile-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <h3 class="card-title">Huy Hiệu & Thành Tích</h3>
                </div>
                <div class="achievements-grid" id="achievementsGrid">
                    <!-- Achievements will be populated by JavaScript -->
                </div>
                <div class="pagination-container" id="achievementsPagination" style="display: none;">
                    <div class="pagination-info" id="achievementsPaginationInfo"></div>
                    <div class="pagination-controls">
                        <button class="pagination-btn" id="achievementsPrevBtn" onclick="changeAchievementsPage(-1)">
                            <i class="fas fa-chevron-left"></i> Trước
                        </button>
                        <button class="pagination-btn" id="achievementsNextBtn" onclick="changeAchievementsPage(1)">
                            Sau <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Quick Actions Card -->
            <div class="profile-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <h3 class="card-title">Thao Tác Nhanh</h3>
                </div>
                <div class="quick-actions">
                    <a href="../classes/" class="action-btn">
                        <i class="fas fa-chalkboard-teacher"></i> Lớp học của tôi
                    </a>
                    <a href="../rankings/" class="action-btn">
                        <i class="fas fa-chart-line"></i> Bảng xếp hạng
                    </a>
                    <a href="../achievements/" class="action-btn">
                        <i class="fas fa-star"></i> Thành tích
                    </a>
                    <a href="../admin/" class="action-btn" id="adminQuickAction" style="display: none;">
                        <i class="fas fa-cog"></i> Quản trị
                    </a>
                </div>
            </div>

            <!-- Personal Info Card -->
            <div class="profile-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-user-edit"></i>
                    </div>
                    <h3 class="card-title">Thông Tin Cá Nhân</h3>
                </div>
                <div id="personalInfoContent">
                    <!-- Personal info will be populated by JavaScript -->
                </div>
                <div style="text-align: center; margin-top: 15px;">
                    <a href="index.html" class="action-btn">
                        <i class="fas fa-edit"></i> Chỉnh sửa thông tin
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 – All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Firebase and JavaScript -->
    <script type="module">
        // Firebase imports
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js';
        import { getAuth, onAuthStateChanged, signOut } from 'https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js';
        import { getFirestore, doc, getDoc, setDoc, updateDoc, deleteDoc, collection, query, where, orderBy, limit, getDocs } from 'https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js';

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        // Check authentication and load profile
        onAuthStateChanged(auth, async (user) => {
            if (user) {
                await loadUserProfile(user);
                await loadInboxMessages(user);
                await loadAchievements(user);
                await updateNotificationBadge();
            } else {
                // Redirect to login if not authenticated
                window.location.href = 'index.html';
            }
        });

        // Load user profile data
        async function loadUserProfile(user) {
            try {
                const userDoc = await getDoc(doc(db, "users", user.uid));
                if (userDoc.exists()) {
                    const userData = userDoc.data();
                    
                    // Update profile info
                    document.getElementById('profileName').textContent = userData.fullName || user.displayName || 'Học viên';
                    document.getElementById('profileEmail').textContent = user.email;
                    
                    // Update avatar
                    if (userData.avatar) {
                        document.getElementById('profileAvatarImg').src = userData.avatar;
                    }
                    
                    // Update stats
                    document.getElementById('statClasses').textContent = userData.classCount || 0;
                    document.getElementById('statAssignments').textContent = userData.assignmentCount || 0;
                    document.getElementById('statRank').textContent = userData.rank || '-';
                    document.getElementById('statAchievements').textContent = userData.achievementCount || 0;
                    
                    // Show admin quick action if user is admin
                    if (user.email === '<EMAIL>') {
                        document.getElementById('adminQuickAction').style.display = 'flex';
                    }
                    
                    // Load personal info
                    loadPersonalInfo(userData);
                }
            } catch (error) {
                console.error("Error loading user profile:", error);
            }
        }

        // Load personal info
        function loadPersonalInfo(userData) {
            const personalInfoContent = document.getElementById('personalInfoContent');
            personalInfoContent.innerHTML = `
                <div style="display: grid; gap: 15px;">
                    <div style="display: flex; justify-content: space-between; padding: 10px 0; border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                        <span style="font-weight: 500; color: rgba(255, 255, 255, 0.8);">Họ và tên:</span>
                        <span style="color: white;">${userData.fullName || 'Chưa cập nhật'}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; padding: 10px 0; border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                        <span style="font-weight: 500; color: rgba(255, 255, 255, 0.8);">Giới tính:</span>
                        <span style="color: white;">${userData.gender || 'Chưa cập nhật'}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; padding: 10px 0; border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                        <span style="font-weight: 500; color: rgba(255, 255, 255, 0.8);">Ngày sinh:</span>
                        <span style="color: white;">${userData.birthdate || 'Chưa cập nhật'}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; padding: 10px 0; border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                        <span style="font-weight: 500; color: rgba(255, 255, 255, 0.8);">Lớp quan tâm:</span>
                        <span style="color: white;">${userData.courseClass || 'Chưa chọn'}</span>
                    </div>
                </div>
            `;
        }

        // Pagination variables for inbox
        let currentInboxPage = 1;
        const inboxMessagesPerPage = 5;
        let totalInboxMessages = 0;
        let allInboxMessages = [];

        // Load inbox messages from Firestore with pagination
        async function loadInboxMessages(user, page = 1) {
            const inboxContent = document.getElementById('inboxContent');
            const paginationContainer = document.getElementById('inboxPagination');
            const paginationInfo = document.getElementById('inboxPaginationInfo');

            try {
                // Get all messages first to calculate total
                const allMessagesQuery = query(
                    collection(db, "messages"),
                    where("recipientEmail", "==", user.email),
                    orderBy("timestamp", "desc")
                );

                const allMessagesSnapshot = await getDocs(allMessagesQuery);
                allInboxMessages = [];

                allMessagesSnapshot.forEach((doc) => {
                    allInboxMessages.push({
                        id: doc.id,
                        ...doc.data()
                    });
                });

                totalInboxMessages = allInboxMessages.length;
                currentInboxPage = page;

                if (totalInboxMessages === 0) {
                    inboxContent.innerHTML = `
                        <div class="empty-inbox">
                            <div class="empty-icon">
                                <i class="fas fa-inbox"></i>
                            </div>
                            <h4>Hộp thư trống</h4>
                            <p>Bạn chưa có tin nhắn nào. Các thông báo về bài học mới, cuộc thi, thành tích BXH và học phí sẽ xuất hiện tại đây.</p>
                        </div>
                    `;
                    paginationContainer.style.display = 'none';
                } else {
                    // Calculate pagination
                    const startIndex = (page - 1) * inboxMessagesPerPage;
                    const endIndex = startIndex + inboxMessagesPerPage;
                    const pageMessages = allInboxMessages.slice(startIndex, endIndex);
                    const totalPages = Math.ceil(totalInboxMessages / inboxMessagesPerPage);

                    // Display messages
                    inboxContent.innerHTML = pageMessages.map(msg => `
                        <div class="inbox-item ${!msg.read ? 'unread' : ''}" onclick="openMessage('${msg.id}')">
                            <div class="message-avatar">
                                ${msg.senderName ? msg.senderName.charAt(0) : 'S'}
                            </div>
                            <div class="message-content">
                                <div class="message-title">${msg.title}</div>
                                <div class="message-preview">${msg.content.substring(0, 50)}...</div>
                            </div>
                            <div class="message-actions">
                                <div class="message-time">${formatTime(msg.timestamp)}</div>
                                <button class="delete-message-btn" onclick="event.stopPropagation(); deleteMessage('${msg.id}')" title="Xóa tin nhắn">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    `).join('');

                    // Update pagination
                    if (totalPages > 1) {
                        paginationContainer.style.display = 'flex';
                        paginationInfo.textContent = `Trang ${page} / ${totalPages} (${totalInboxMessages} tin nhắn)`;

                        document.getElementById('inboxPrevBtn').disabled = page === 1;
                        document.getElementById('inboxNextBtn').disabled = page === totalPages;
                    } else {
                        paginationContainer.style.display = 'none';
                    }
                }
            } catch (error) {
                console.error("Error loading messages:", error);
                inboxContent.innerHTML = `
                    <div class="empty-inbox">
                        <div class="empty-icon">
                            <i class="fas fa-inbox"></i>
                        </div>
                        <h4>Hộp thư trống</h4>
                        <p>Bạn chưa có tin nhắn nào. Các thông báo về bài học mới, cuộc thi, thành tích BXH và học phí sẽ xuất hiện tại đây.</p>
                    </div>
                `;
                paginationContainer.style.display = 'none';
            }
        }

        // Change inbox page
        window.changeInboxPage = function(direction) {
            const newPage = currentInboxPage + direction;
            const totalPages = Math.ceil(totalInboxMessages / inboxMessagesPerPage);

            if (newPage >= 1 && newPage <= totalPages) {
                const user = auth.currentUser;
                if (user) {
                    loadInboxMessages(user, newPage);
                }
            }
        };

        // Format timestamp to readable time
        function formatTime(timestamp) {
            if (!timestamp) return 'Vừa xong';

            const now = new Date();
            const messageTime = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
            const diffMs = now - messageTime;
            const diffMins = Math.floor(diffMs / 60000);
            const diffHours = Math.floor(diffMs / 3600000);
            const diffDays = Math.floor(diffMs / 86400000);

            if (diffMins < 1) return 'Vừa xong';
            if (diffMins < 60) return `${diffMins} phút trước`;
            if (diffHours < 24) return `${diffHours} giờ trước`;
            if (diffDays < 7) return `${diffDays} ngày trước`;
            return messageTime.toLocaleDateString('vi-VN');
        }

        // Pagination variables for achievements
        let currentAchievementsPage = 1;
        const achievementsPerPage = 10;
        let totalAchievements = 0;
        let allBadges = [];
        let earnedBadges = [];

        // Load achievements and badges with pagination
        async function loadAchievements(user, page = 1) {
            const achievementsGrid = document.getElementById('achievementsGrid');
            const paginationContainer = document.getElementById('achievementsPagination');
            const paginationInfo = document.getElementById('achievementsPaginationInfo');

            try {
                // Load available badges
                const badgesResponse = await fetch('../assets/images/badges/badges.json');
                const badgesData = await badgesResponse.json();
                allBadges = badgesData.badges;

                // Get user's earned badges from Firestore
                const userDoc = await getDoc(doc(db, "users", user.uid));
                const userData = userDoc.data();
                earnedBadges = userData.badges || [];

                totalAchievements = allBadges.length;
                currentAchievementsPage = page;

                if (totalAchievements === 0) {
                    achievementsGrid.innerHTML = `
                        <div class="empty-achievements">
                            <div class="empty-icon">
                                <i class="fas fa-trophy"></i>
                            </div>
                            <h4>Chưa có huy hiệu</h4>
                            <p>Hoàn thành các bài tập và tham gia cuộc thi để nhận huy hiệu!</p>
                        </div>
                    `;
                    paginationContainer.style.display = 'none';
                } else {
                    // Calculate pagination
                    const startIndex = (page - 1) * achievementsPerPage;
                    const endIndex = startIndex + achievementsPerPage;
                    const pageBadges = allBadges.slice(startIndex, endIndex);
                    const totalPages = Math.ceil(totalAchievements / achievementsPerPage);

                    // Create badges HTML
                    const badgesHTML = pageBadges.map(badge => {
                        const isEarned = earnedBadges.includes(badge.id);
                        const rarityClass = badge.rarity || 'common';

                        return `
                            <div class="badge-item ${isEarned ? 'earned' : 'unearned'}" data-badge-id="${badge.id}">
                                <div class="badge-image ${rarityClass}">
                                    <img src="../assets/images/badges/${badge.image}" alt="${badge.name}"
                                         onerror="this.src='../assets/images/logo.jpg'">
                                    ${isEarned ? '' : '<div class="badge-lock"><i class="fas fa-lock"></i></div>'}
                                </div>
                                <div class="badge-info">
                                    <div class="badge-name">${badge.name}</div>
                                    <div class="badge-description">${badge.description}</div>
                                </div>
                            </div>
                        `;
                    }).join('');

                    achievementsGrid.innerHTML = `
                        <div class="badges-section">
                            <h4><i class="fas fa-trophy"></i> Huy hiệu của tôi</h4>
                            <div class="badges-grid">
                                ${badgesHTML}
                            </div>
                        </div>
                    `;

                    // Update pagination
                    if (totalPages > 1) {
                        paginationContainer.style.display = 'flex';
                        paginationInfo.textContent = `Trang ${page} / ${totalPages} (${totalAchievements} huy hiệu)`;

                        document.getElementById('achievementsPrevBtn').disabled = page === 1;
                        document.getElementById('achievementsNextBtn').disabled = page === totalPages;
                    } else {
                        paginationContainer.style.display = 'none';
                    }
                }

                // Update achievement count
                document.getElementById('statAchievements').textContent = earnedBadges.length;

            } catch (error) {
                console.error("Error loading achievements:", error);
                achievementsGrid.innerHTML = `
                    <div class="empty-achievements">
                        <div class="empty-icon">
                            <i class="fas fa-trophy"></i>
                        </div>
                        <h4>Chưa có huy hiệu</h4>
                        <p>Hoàn thành các bài tập và tham gia cuộc thi để nhận huy hiệu!</p>
                    </div>
                `;
                paginationContainer.style.display = 'none';
            }
        }

        // Change achievements page
        window.changeAchievementsPage = function(direction) {
            const newPage = currentAchievementsPage + direction;
            const totalPages = Math.ceil(totalAchievements / achievementsPerPage);

            if (newPage >= 1 && newPage <= totalPages) {
                const user = auth.currentUser;
                if (user) {
                    loadAchievements(user, newPage);
                }
            }
        };

        // Update notification badge based on unread messages
        async function updateNotificationBadge() {
            const inboxBadge = document.getElementById('inboxNotificationBadge');

            try {
                const user = auth.currentUser;
                if (!user) {
                    if (inboxBadge) inboxBadge.style.display = 'none';
                    return;
                }

                // Count unread messages
                const unreadQuery = query(
                    collection(db, "messages"),
                    where("recipientEmail", "==", user.email),
                    where("read", "==", false)
                );

                const unreadSnapshot = await getDocs(unreadQuery);
                const unreadCount = unreadSnapshot.size;

                if (inboxBadge) {
                    if (unreadCount > 0) {
                        inboxBadge.textContent = unreadCount;
                        inboxBadge.style.display = 'flex';
                    } else {
                        inboxBadge.style.display = 'none';
                    }
                }
            } catch (error) {
                console.warn("Messages collection not accessible:", error.message);
                // Hide badge silently for permission errors
                if (inboxBadge) inboxBadge.style.display = 'none';
            }
        }

        // Global functions
        window.openAvatarModal = function() {
            // Redirect to main account page for avatar selection
            window.location.href = 'index.html#avatar';
        };

        window.openInboxModal = function() {
            alert('Inbox modal sẽ được implement sau!');
        };

        // Clear all messages for current user (for testing)
        window.clearAllMessages = async function() {
            if (!confirm('Bạn có chắc chắn muốn xóa TẤT CẢ tin nhắn?')) {
                return;
            }

            try {
                const user = auth.currentUser;
                if (!user) return;

                const messagesQuery = query(
                    collection(db, "messages"),
                    where("recipientEmail", "==", user.email)
                );

                const messagesSnapshot = await getDocs(messagesQuery);
                const deletePromises = [];

                messagesSnapshot.forEach((doc) => {
                    deletePromises.push(deleteDoc(doc.ref));
                });

                await Promise.all(deletePromises);

                // Reload messages and update badge
                await loadInboxMessages(user, 1);
                await updateNotificationBadge();

                showNotification(`Đã xóa ${deletePromises.length} tin nhắn!`, 'success');
            } catch (error) {
                console.error("Error clearing messages:", error);
                showNotification('Có lỗi xảy ra khi xóa tin nhắn!', 'error');
            }
        };

        // Open message modal
        window.openMessage = async function(messageId) {
            try {
                const user = auth.currentUser;
                if (!user) return;

                // Find message in allInboxMessages
                const message = allInboxMessages.find(msg => msg.id === messageId);
                if (!message) return;

                // Populate modal
                document.getElementById('modalTitle').textContent = message.title;
                document.getElementById('modalAvatar').textContent = message.senderName ? message.senderName.charAt(0) : 'S';
                document.getElementById('modalSender').textContent = message.senderName || 'Hệ thống';
                document.getElementById('modalTime').textContent = formatTime(message.timestamp);
                document.getElementById('modalContent').textContent = message.content;

                // Show modal
                document.getElementById('messageModal').classList.add('show');

                // Mark as read if not already
                if (!message.read) {
                    await updateDoc(doc(db, "messages", messageId), {
                        read: true
                    });

                    // Update local data
                    message.read = true;

                    // Refresh display and badge
                    await loadInboxMessages(user, currentInboxPage);
                    await updateNotificationBadge();
                }
            } catch (error) {
                console.error("Error opening message:", error);
            }
        };

        // Close message modal
        window.closeMessageModal = function() {
            document.getElementById('messageModal').classList.remove('show');
        };

        // Close modal when clicking outside
        document.getElementById('messageModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeMessageModal();
            }
        });

        // Delete message function
        window.deleteMessage = async function(messageId) {
            if (!confirm('Bạn có chắc chắn muốn xóa tin nhắn này?')) {
                return;
            }

            try {
                const user = auth.currentUser;
                if (!user) return;

                // Delete message from Firestore
                await deleteDoc(doc(db, "messages", messageId));

                // Reload messages with current page
                await loadInboxMessages(user, currentInboxPage);
                await updateNotificationBadge();

                // Show success message
                showNotification('Đã xóa tin nhắn thành công!', 'success');
            } catch (error) {
                console.error("Error deleting message:", error);
                showNotification('Có lỗi xảy ra khi xóa tin nhắn!', 'error');
            }
        };

        // Show notification function
        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#48bb78' : type === 'error' ? '#f56565' : '#4299e1'};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                font-weight: 500;
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // Remove after 3 seconds
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }
    </script>

    <!-- Message Modal -->
    <div id="messageModal" class="message-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">Chi tiết tin nhắn</h3>
                <button class="modal-close" onclick="closeMessageModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="modal-message-info">
                    <div class="modal-avatar" id="modalAvatar">S</div>
                    <div class="modal-sender-info">
                        <h4 id="modalSender">Hệ thống</h4>
                        <p id="modalTime">Vừa xong</p>
                    </div>
                </div>
                <div class="modal-message-content" id="modalContent">
                    <!-- Message content will be populated here -->
                </div>
            </div>
        </div>
    </div>

    <script src="../assets/js/script.js"></script>
</body>
</html>
