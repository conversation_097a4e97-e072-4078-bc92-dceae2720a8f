<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bảng Xếp <PERSON> - <PERSON>thon</title>
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="../assets/images/favicon.png">
    <link rel="shortcut icon" type="image/png" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="72x72" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="114x114" href="../assets/images/favicon.png">

    <link rel="stylesheet" href="../assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        /* Starfield Background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(2px 2px at 20px 30px, #fff, transparent),
                radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
                radial-gradient(1px 1px at 90px 40px, #fff, transparent),
                radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
                radial-gradient(2px 2px at 160px 30px, #fff, transparent);
            background-repeat: repeat;
            background-size: 200px 100px;
            animation: sparkle 3s linear infinite;
            opacity: 0.3;
            z-index: -1;
        }

        @keyframes sparkle {
            0% { transform: translateY(0px); }
            100% { transform: translateY(-100px); }
        }

        /* Top Stats Bar */
        .top-stats-bar {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 20px;
            margin: 120px auto 40px;
            max-width: 1000px;
            display: flex;
            justify-content: space-around;
            align-items: center;
            color: white;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .stat-item {
            text-align: center;
            padding: 10px 20px;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.8);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .page-header {
            text-align: center;
            margin: 40px 0 170px;
            color: white;
        }

        .page-header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: textGlow 2s ease-in-out infinite alternate;
        }

        @keyframes textGlow {
            from {
                text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
            }
            to {
                text-shadow: 0 0 30px rgba(255, 215, 0, 0.8);
            }
        }

        .page-header p {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.3rem;
            max-width: 700px;
            margin: 0 auto;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        /* Top 3 Podium - Gaming Style */
        .podium-container {
            max-width: 1200px;
            margin: 60px auto 80px;
            padding: 0 20px;
        }

        .podium {
            display: flex;
            justify-content: center;
            align-items: end;
            gap: 30px;
            margin-bottom: 40px;
            position: relative;
            height: 400px; /* Fixed height for podium container */
        }

        .podium-place {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 40px 25px;
            text-align: center;
            position: relative;
            transition: all 0.4s ease;
            border: 2px solid rgba(255, 255, 255, 0.1);
            width: 200px; /* Fixed width for all podium places */
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .podium-place::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }

        .podium-place:hover::before {
            transform: translateX(100%);
        }

        .podium-place:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.3);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
        }

        .podium-place.first {
            order: 2;
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(255, 165, 0, 0.1));
            border: 2px solid #FFD700;
            margin-bottom: 80px; /* Raise the first place higher */
            box-shadow: 0 25px 50px rgba(255, 215, 0, 0.3), 0 0 30px rgba(255, 215, 0, 0.2);
            cursor: pointer;
        }

        .podium-place.first:hover {
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.3), rgba(255, 165, 0, 0.2));
            border-color: #FFD700;
            box-shadow: 0 30px 60px rgba(255, 215, 0, 0.4), 0 0 40px rgba(255, 215, 0, 0.3);
        }

        .podium-place.second {
            order: 1;
            background: linear-gradient(135deg, rgba(192, 192, 192, 0.2), rgba(168, 168, 168, 0.1));
            border: 2px solid #C0C0C0;
            margin-bottom: 0; /* Same level as third place */
            box-shadow: 0 20px 40px rgba(192, 192, 192, 0.2);
            cursor: pointer;
        }

        .podium-place.second:hover {
            background: linear-gradient(135deg, rgba(192, 192, 192, 0.3), rgba(168, 168, 168, 0.2));
            border-color: #C0C0C0;
            box-shadow: 0 25px 50px rgba(192, 192, 192, 0.3);
        }

        .podium-place.third {
            order: 3;
            background: linear-gradient(135deg, rgba(205, 127, 50, 0.2), rgba(184, 134, 11, 0.1));
            border: 2px solid #CD7F32;
            margin-bottom: 0; /* Same level as second place */
            box-shadow: 0 20px 40px rgba(205, 127, 50, 0.2);
            cursor: pointer;
        }

        .podium-place.third:hover {
            background: linear-gradient(135deg, rgba(205, 127, 50, 0.3), rgba(184, 134, 11, 0.2));
            border-color: #CD7F32;
            box-shadow: 0 25px 50px rgba(205, 127, 50, 0.3);
        }

        .podium-rank {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
        }

        .podium-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            margin: 0 auto 20px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            overflow: hidden;
            position: relative;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .podium-avatar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 50%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            animation: avatarShine 3s ease-in-out infinite;
        }

        @keyframes avatarShine {
            0%, 100% { opacity: 0; transform: rotate(0deg); }
            50% { opacity: 1; transform: rotate(180deg); }
        }

        .podium-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .podium-name {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        .podium-score {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 8px;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .podium-time {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 8px;
            color: rgba(255, 255, 255, 0.9);
            background: rgba(255, 255, 255, 0.1);
            padding: 6px 12px;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
            display: inline-block;
        }

        .podium-class {
            font-size: 1rem;
            color: rgba(255, 255, 255, 0.8);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        }

        /* Leaderboard Cards - Gaming Style */
        .leaderboard-container {
            max-width: 1000px;
            margin: 0 auto 50px;
            padding: 0 20px;
        }

        .leaderboard-title {
            text-align: center;
            color: white;
            font-size: 2.2rem;
            font-weight: 600;
            margin-bottom: 40px;
            text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .leaderboard-item {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 20px;
            display: grid;
            grid-template-columns: 70px 1fr auto auto auto;
            align-items: center;
            gap: 25px;
            transition: all 0.4s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: white;
            position: relative;
            overflow: hidden;
        }

        .leaderboard-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.05), transparent);
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }

        .leaderboard-item:hover::before {
            transform: translateX(100%);
        }

        .leaderboard-item:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(255, 215, 0, 0.3);
        }

        .rank {
            font-size: 1.6rem;
            font-weight: 700;
            text-align: center;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        .student-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .student-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            overflow: hidden;
            border: 3px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            position: relative;
        }

        .student-avatar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 50%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            animation: avatarGlow 2s ease-in-out infinite;
        }

        @keyframes avatarGlow {
            0%, 100% { opacity: 0; }
            50% { opacity: 1; }
        }

        .student-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .student-name {
            font-weight: 600;
            color: white;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.1rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        .student-badge {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            border: 2px solid #ffd700;
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
            color: #333;
            box-shadow: 0 4px 8px rgba(255, 215, 0, 0.4), 0 0 15px rgba(255, 215, 0, 0.2);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .student-badge:hover {
            transform: scale(1.2);
            box-shadow: 0 6px 12px rgba(255, 215, 0, 0.6), 0 0 20px rgba(255, 215, 0, 0.3);
        }

        .student-badge img {
            width: 18px;
            height: 18px;
            border-radius: 50%;
        }

        .student-class {
            color: rgba(255, 255, 255, 0.7);
            font-size: 1rem;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        }

        .score {
            font-weight: 700;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 1.4rem;
            text-align: center;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .completion-time {
            font-weight: 600;
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.1rem;
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 8px 12px;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* Rank Change Indicator */
        .rank-change {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .rank-change.up {
            color: #4CAF50;
        }

        .rank-change.down {
            color: #f44336;
        }

        .rank-change.new {
            color: #2196F3;
        }

        .rank-change i {
            font-size: 1rem;
        }
        
        .rank-badge {
            display: inline-flex;
            justify-content: center;
            align-items: center;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            font-weight: 700;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        .rank-badge-1 {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4), 0 0 20px rgba(255, 215, 0, 0.2);
            border: 2px solid #FFD700;
        }

        .rank-badge-2 {
            background: linear-gradient(135deg, #C0C0C0, #A8A8A8);
            box-shadow: 0 5px 15px rgba(192, 192, 192, 0.4);
            border: 2px solid #C0C0C0;
        }

        .rank-badge-3 {
            background: linear-gradient(135deg, #CD7F32, #B8860B);
            box-shadow: 0 5px 15px rgba(205, 127, 50, 0.4);
            border: 2px solid #CD7F32;
        }

        .rank-badge-other {
            background: linear-gradient(135deg, #4285F4, #1976D2);
            box-shadow: 0 5px 15px rgba(66, 133, 244, 0.4);
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .last-updated {
            text-align: center;
            color: rgba(255, 255, 255, 0.7);
            font-size: 1rem;
            margin-bottom: 50px;
            font-style: italic;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        }

        .pagination-container {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 40px 0;
            gap: 25px;
        }

        .pagination-btn {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.2);
            padding: 15px 20px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 1.3rem;
            transition: all 0.4s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 60px;
            height: 60px;
            backdrop-filter: blur(10px);
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        .pagination-btn:hover:not(:disabled) {
            background: rgba(255, 215, 0, 0.2);
            border-color: #FFD700;
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 10px 20px rgba(255, 215, 0, 0.3);
        }

        .pagination-btn:disabled {
            background: rgba(255, 255, 255, 0.05);
            border-color: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.3);
            cursor: not-allowed;
            transform: none;
        }

        .pagination-info {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            padding: 15px 25px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            font-weight: 600;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        .total-students {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            max-width: 700px;
            margin-left: auto;
            margin-right: auto;
            margin-bottom: 40px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: white;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .total-students h3 {
            font-size: 1.6rem;
            margin-bottom: 15px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .total-students p {
            margin: 0;
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .badge-icon {
            width: 24px;
            height: 24px;
            margin-left: 8px;
            vertical-align: middle;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
        }

        /* Floating Animation for Background Elements */
        .floating-bg-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            overflow: hidden;
        }

        .floating-element {
            position: absolute;
            font-size: 2rem;
            opacity: 0.1;
            animation: float 15s infinite linear;
        }

        .floating-element:nth-child(1) { left: 10%; animation-delay: 0s; }
        .floating-element:nth-child(2) { left: 20%; animation-delay: 3s; }
        .floating-element:nth-child(3) { left: 30%; animation-delay: 6s; }
        .floating-element:nth-child(4) { left: 40%; animation-delay: 9s; }
        .floating-element:nth-child(5) { left: 50%; animation-delay: 12s; }
        .floating-element:nth-child(6) { left: 60%; animation-delay: 2s; }
        .floating-element:nth-child(7) { left: 70%; animation-delay: 5s; }
        .floating-element:nth-child(8) { left: 80%; animation-delay: 8s; }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 0.1;
            }
            90% {
                opacity: 0.1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        /* Entrance Animations */
        .fade-in-up {
            animation: fadeInUp 0.8s ease-out forwards;
        }

        .fade-in-left {
            animation: fadeInLeft 0.8s ease-out forwards;
        }

        .fade-in-right {
            animation: fadeInRight 0.8s ease-out forwards;
        }

        @keyframes fadeInLeft {
            from {
                opacity: 0;
                transform: translateX(-50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes fadeInRight {
            from {
                opacity: 0;
                transform: translateX(50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .loading {
            text-align: center;
            padding: 80px;
            color: white;
        }

        .loading i {
            font-size: 4rem;
            animation: spin 1s linear infinite;
            margin-bottom: 30px;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .no-data {
            text-align: center;
            padding: 80px;
            color: white;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            margin: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .no-data i {
            font-size: 5rem;
            margin-bottom: 30px;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
        }

        .no-data h3 {
            font-size: 1.8rem;
            margin-bottom: 15px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        .no-data p {
            opacity: 0.9;
            font-size: 1.1rem;
            line-height: 1.6;
        }

        @media (max-width: 768px) {
            .top-stats-bar {
                flex-direction: column;
                gap: 15px;
                padding: 20px 15px;
                margin: 100px 15px 30px;
            }

            .stat-item {
                padding: 5px 10px;
            }

            .stat-value {
                font-size: 1.5rem;
            }

            .stat-label {
                font-size: 0.8rem;
            }

            .page-header h1 {
                font-size: 2.2rem;
            }

            .page-header p {
                font-size: 1.1rem;
                padding: 0 10px;
            }

            .podium {
                flex-direction: column;
                align-items: center;
                gap: 20px;
                height: auto; /* Remove fixed height on mobile */
            }

            .podium-place {
                width: 100%;
                max-width: 280px;
                margin-bottom: 15px;
                padding: 30px 20px;
            }

            .podium-place.first {
                order: 1;
                margin-bottom: 15px; /* Reset margin on mobile */
            }

            .podium-place.second {
                order: 2;
                margin-bottom: 15px;
            }

            .podium-place.third {
                order: 3;
                margin-bottom: 15px;
            }

            .podium-rank {
                font-size: 2.5rem;
            }

            .podium-avatar {
                width: 80px;
                height: 80px;
            }

            .podium-name {
                font-size: 1.1rem;
            }

            .podium-score {
                font-size: 1.5rem;
            }

            .podium-time {
                font-size: 1rem;
                padding: 4px 8px;
            }

            .leaderboard-item {
                grid-template-columns: 45px 1fr 50px 60px 40px;
                padding: 20px 15px;
                gap: 15px;
                border-radius: 15px;
            }

            .rank {
                width: 40px;
                height: 40px;
                font-size: 1.2rem;
            }

            .student-avatar {
                width: 50px;
                height: 50px;
            }

            .student-name {
                font-size: 1rem;
            }

            .student-class {
                font-size: 0.9rem;
            }

            .score {
                font-size: 1.2rem;
            }

            .completion-time {
                font-size: 0.9rem;
                padding: 6px 8px;
            }

            .rank-change {
                font-size: 0.8rem;
            }

            .rank-change i {
                font-size: 0.8rem;
            }

            .pagination-btn {
                padding: 12px 15px;
                font-size: 1.1rem;
                min-width: 50px;
                height: 50px;
            }

            .pagination-info {
                padding: 12px 20px;
                font-size: 0.9rem;
            }

            .leaderboard-title {
                font-size: 1.8rem;
                margin-bottom: 30px;
            }

            .total-students {
                padding: 25px 20px;
                margin: 30px 15px;
            }

            .total-students h3 {
                font-size: 1.4rem;
            }

            .total-students p {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../index.html">Trang Chủ</a></li>
                    <li><a href="../classes/">Lớp Học</a></li>
                    <li><a href="../achievements/">Thành Tích</a></li>
                    <li><a href="../auth/register.html">Đăng Ký</a></li>
                    <li><a href="index.html" class="active">Bảng Xếp Hạng</a></li>
                    <li><a href="../research/">Sự kiện</a></li>
                    <li><a href="../auth/">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Rankings Section -->
    <section>
        <div class="container">
            <!-- Top Stats Bar -->
            <div class="top-stats-bar fade-in-up" id="topStatsBar">
                <div class="stat-item">
                    <div class="stat-value" id="totalStudents">0</div>
                    <div class="stat-label">Học viên tham gia</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="totalAssignments">0</div>
                    <div class="stat-label">Bài tập hoàn thành</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="averageScore">0</div>
                    <div class="stat-label">Điểm trung bình</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="topScore">0</div>
                    <div class="stat-label">Điểm cao nhất</div>
                </div>
            </div>

            <div class="page-header fade-in-up">
                <h1>Bảng Xếp Hạng Học Viên</h1>
                <p>Bảng xếp hạng tất cả học viên đã tham gia làm bài tập dựa trên điểm tổng các bài kiểm tra. Khi điểm bằng nhau, học viên hoàn thành nhanh hơn sẽ xếp hạng cao hơn.</p>
            </div>

            <!-- Top 3 Podium -->
            <div class="podium-container" id="podiumContainer" style="display: none;">
                <div class="podium" id="podium">
                    <!-- Top 3 will be displayed here -->
                </div>
            </div>

            <!-- Leaderboard Title -->
            <div class="leaderboard-title" id="leaderboardTitle" style="display: none;">
                Bảng Xếp Hạng Tổng Thể
            </div>

            <div class="leaderboard-container" id="leaderboardContainer">
                <!-- Leaderboard content will be loaded here -->
            </div>

            <div class="pagination-container" id="paginationContainer" style="display: none;">
                <button class="pagination-btn" id="prevBtn" onclick="changePage(-1)">
                    ⭠
                </button>
                <div class="pagination-info" id="paginationInfo">
                    Trang 1 / 1
                </div>
                <button class="pagination-btn" id="nextBtn" onclick="changePage(1)">
                    ⮕
                </button>
            </div>
        </div>

        <div class="last-updated" id="lastUpdated">
            <!-- Last updated time will be displayed here -->
        </div>
    </section>

    <!-- Floating Background Elements -->
    <div class="floating-bg-elements">
        <div class="floating-element">🏆</div>
        <div class="floating-element">⭐</div>
        <div class="floating-element">🎯</div>
        <div class="floating-element">💎</div>
        <div class="floating-element">🚀</div>
        <div class="floating-element">⚡</div>
        <div class="floating-element">🔥</div>
        <div class="floating-element">💫</div>
    </div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";
        import { getFirestore, collection, getDocs, query, orderBy, limit } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        // Pagination variables
        let allRankings = [];
        let previousRankings = [];
        let currentPage = 1;
        const itemsPerPage = 10;
        const adminEmail = '<EMAIL>';
        let availableBadges = [];

        // Load available badges
        async function loadBadges() {
            try {
                const response = await fetch('../assets/images/badges/badges.json');
                const badgesData = await response.json();
                availableBadges = badgesData.badges;
            } catch (error) {
                console.error("Error loading badges:", error);
                availableBadges = [];
            }
        }

        // Get badge icon for student
        function getBadgeIcon(studentBadges) {
            if (!studentBadges || studentBadges.length === 0) return '';

            // Show the first badge (highest priority)
            const firstBadgeId = studentBadges[0];
            const badge = availableBadges.find(b => b.id === firstBadgeId);

            if (badge) {
                return `<div class="student-badge" title="${badge.name}: ${badge.description}">
                    <img src="../assets/images/badges/${badge.image}" alt="${badge.name}"
                         onerror="this.src='../assets/images/logo.jpg'">
                </div>`;
            }
            return '';
        }

        // Load real rankings from Firebase
        async function loadRankings() {
            try {
                // Get all users first, then filter and sort
                const querySnapshot = await getDocs(collection(db, "users"));
                const rankings = [];

                querySnapshot.forEach((doc) => {
                    const userData = doc.data();
                    // Exclude admin and include users who have completed profile and have done assignments
                    if (userData.fullName && userData.fullName.trim() !== '' && userData.email !== adminEmail) {
                        const totalScore = userData.totalScore || 0;
                        const assignmentCount = userData.assignmentCount || 0;
                        // Include users who have done at least one assignment (assignmentCount > 0)
                        if (assignmentCount > 0) {
                            rankings.push({
                                name: userData.fullName,
                                class: userData.courseClass || 'Chưa phân lớp',
                                score: totalScore,
                                avatar: userData.avatar || '../assets/images/user.png',
                                lastAssignmentDate: userData.lastAssignmentDate || null,
                                totalCompletionTime: userData.totalCompletionTime || 0,
                                email: userData.email,
                                assignmentCount: assignmentCount,
                                badges: userData.badges || [],
                                userId: doc.id
                            });
                        }
                    }
                });

                // Sort by score descending, then by total completion time ascending (shorter time wins ties)
                rankings.sort((a, b) => {
                    if (b.score !== a.score) {
                        return b.score - a.score;
                    }
                    // If scores are equal, shorter completion time wins
                    if (a.totalCompletionTime && b.totalCompletionTime) {
                        return a.totalCompletionTime - b.totalCompletionTime;
                    }
                    // If one has completion time and other doesn't, prioritize the one with time
                    if (a.totalCompletionTime && !b.totalCompletionTime) return -1;
                    if (!a.totalCompletionTime && b.totalCompletionTime) return 1;
                    return 0;
                });

                // Store previous rankings for comparison
                const storedRankings = localStorage.getItem('currentRankings');
                if (storedRankings) {
                    localStorage.setItem('previousRankings', storedRankings);
                }

                // Store current rankings
                localStorage.setItem('currentRankings', JSON.stringify(rankings));

                // Store all rankings and reset to first page
                allRankings = rankings;
                currentPage = 1;

                // Update the leaderboard with pagination
                updateLeaderboard();
                updateTotalStudentsInfo();

            } catch (error) {
                console.error("Error loading rankings:", error);
                // Show error state
                allRankings = [];
                updateLeaderboard();
                updateTotalStudentsInfo();
            }
        }

        function updateLeaderboard() {
            const leaderboardContainer = document.getElementById('leaderboardContainer');
            const paginationContainer = document.getElementById('paginationContainer');
            const podiumContainer = document.getElementById('podiumContainer');
            const leaderboardTitle = document.getElementById('leaderboardTitle');

            // Calculate pagination
            const totalPages = Math.ceil(allRankings.length / itemsPerPage);
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const currentRankings = allRankings.slice(startIndex, endIndex);

            // Update podium for top 3
            updatePodium();

            // Start from rank 4 for the leaderboard if we're on the first page
            const startRank = currentPage === 1 ? 4 : startIndex + 1;
            const displayRankings = currentPage === 1 ?
                allRankings.slice(3, endIndex) : // Skip top 3 on first page
                currentRankings;

            let html = '';

            if (allRankings.length === 0) {
                html += `
                    <div class="no-data">
                        <i class="fas fa-trophy"></i>
                        <h3>Chưa có dữ liệu xếp hạng</h3>
                        <p>Bảng xếp hạng sẽ được cập nhật khi có học viên tham gia làm các bài kiểm tra và bài tập.
                           Hãy tham gia các lớp học và làm bài tập để xuất hiện trên bảng xếp hạng!</p>
                        <div style="margin-top: 30px;">
                            <a href="../classes/" style="display: inline-block; background: rgba(255, 255, 255, 0.2); color: white; padding: 12px 25px; border-radius: 25px; text-decoration: none; font-weight: 500; margin-right: 15px; transition: all 0.3s; backdrop-filter: blur(10px);">
                                <i class="fas fa-book"></i> Xem Lớp Học
                            </a>
                            <a href="../index.html" style="display: inline-block; background: rgba(255, 255, 255, 0.2); color: white; padding: 12px 25px; border-radius: 25px; text-decoration: none; font-weight: 500; transition: all 0.3s; backdrop-filter: blur(10px);">
                                <i class="fas fa-home"></i> Về Trang Chủ
                            </a>
                        </div>
                    </div>
                `;
                paginationContainer.style.display = 'none';
                podiumContainer.style.display = 'none';
                leaderboardTitle.style.display = 'none';
            } else {
                // Show leaderboard title if we have rankings
                if (allRankings.length > 3) {
                    leaderboardTitle.style.display = 'block';
                }

                displayRankings.forEach((student, index) => {
                    const globalRank = currentPage === 1 ?
                        index + 4 : // Start from rank 4 on first page
                        startIndex + index + 1; // Normal ranking on other pages

                    const className = getClassDisplayName(student.class);

                    // Format completion time
                    const totalMinutes = Math.floor(student.totalCompletionTime / 60);
                    const totalSeconds = student.totalCompletionTime % 60;
                    const timeDisplay = student.totalCompletionTime > 0 ?
                        `${totalMinutes}:${totalSeconds.toString().padStart(2, '0')}` :
                        '--:--';

                    html += `
                        <div class="leaderboard-item">
                            <div class="rank">${globalRank}</div>
                            <div class="student-info">
                                <div class="student-avatar">
                                    <img src="${student.avatar}" alt="${student.name}" onerror="this.src='../assets/images/user.png'">
                                </div>
                                <div class="student-details">
                                    <div class="student-name">
                                        <span onclick="viewStudentProfile('${student.userId}')" style="cursor: pointer;">${student.name}</span>
                                        ${getBadgeIcon(student.badges)}
                                    </div>
                                    <div class="student-class">${className}</div>
                                </div>
                            </div>
                            <div class="score">${student.score}</div>
                            <div class="completion-time">${timeDisplay}</div>
                            <div class="rank-change" id="rankChange_${student.userId}">
                                ${getRankChangeDisplay(student.userId, globalRank)}
                            </div>
                        </div>
                    `;
                });

                // Show pagination if there are multiple pages
                if (totalPages > 1) {
                    paginationContainer.style.display = 'flex';
                    updatePaginationControls(totalPages);
                } else {
                    paginationContainer.style.display = 'none';
                }
            }

            leaderboardContainer.innerHTML = html;

            // Update last updated time
            const lastUpdated = document.getElementById('lastUpdated');
            if (lastUpdated && allRankings.length > 0) {
                const now = new Date();
                lastUpdated.textContent = `Cập nhật lần cuối: ${now.toLocaleDateString('vi-VN')} lúc ${now.toLocaleTimeString('vi-VN')}`;
            }
        }

        function updatePodium() {
            const podiumContainer = document.getElementById('podiumContainer');
            const podium = document.getElementById('podium');

            if (allRankings.length >= 3) {
                podiumContainer.style.display = 'block';

                const top3 = allRankings.slice(0, 3);
                let podiumHTML = '';

                top3.forEach((student, index) => {
                    const rank = index + 1;
                    const className = getClassDisplayName(student.class);
                    const rankText = ['🥇', '🥈', '🥉'][index];
                    const positionClass = ['first', 'second', 'third'][index];

                    // Format completion time
                    const totalMinutes = Math.floor(student.totalCompletionTime / 60);
                    const totalSeconds = student.totalCompletionTime % 60;
                    const timeDisplay = student.totalCompletionTime > 0 ?
                        `${totalMinutes}:${totalSeconds.toString().padStart(2, '0')}` :
                        '--:--';

                    podiumHTML += `
                        <div class="podium-place ${positionClass}" onclick="viewStudentProfile('${student.userId}')">
                            <div class="podium-rank">${rankText}</div>
                            <div class="podium-avatar">
                                <img src="${student.avatar}" alt="${student.name}" onerror="this.src='../assets/images/user.png'">
                            </div>
                            <div class="podium-name">${student.name}</div>
                            <div class="podium-score">${student.score} điểm</div>
                            <div class="podium-time">⏱️ ${timeDisplay}</div>
                            <div class="podium-class">${className}</div>
                            ${getBadgeIcon(student.badges)}
                        </div>
                    `;
                });

                podium.innerHTML = podiumHTML;
            } else {
                podiumContainer.style.display = 'none';
            }
        }

        function updatePaginationControls(totalPages) {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            const paginationInfo = document.getElementById('paginationInfo');

            // Update pagination info
            paginationInfo.textContent = `Trang ${currentPage} / ${totalPages}`;

            // Update button states
            prevBtn.disabled = currentPage === 1;
            nextBtn.disabled = currentPage === totalPages;
        }

        // Make changePage function global
        window.changePage = function(direction) {
            const totalPages = Math.ceil(allRankings.length / itemsPerPage);
            const newPage = currentPage + direction;

            if (newPage >= 1 && newPage <= totalPages) {
                currentPage = newPage;
                updateLeaderboard();
            }
        };

        // Update top stats bar
        function updateStatsBar() {
            if (allRankings.length === 0) return;

            const totalStudents = allRankings.length;
            const totalAssignments = allRankings.reduce((sum, student) => sum + student.assignmentCount, 0);
            const averageScore = Math.round(allRankings.reduce((sum, student) => sum + student.score, 0) / totalStudents);
            const topScore = Math.max(...allRankings.map(student => student.score));

            document.getElementById('totalStudents').textContent = totalStudents;
            document.getElementById('totalAssignments').textContent = totalAssignments;
            document.getElementById('averageScore').textContent = averageScore;
            document.getElementById('topScore').textContent = topScore;
        }

        // Get rank change display
        function getRankChangeDisplay(userId, currentRank) {
            // Load previous rankings from localStorage
            const storedRankings = localStorage.getItem('previousRankings');
            if (!storedRankings) return '';

            const prevRankings = JSON.parse(storedRankings);
            const prevStudent = prevRankings.find(s => s.userId === userId);

            if (!prevStudent) {
                return '<span class="rank-change new"><i class="fas fa-star"></i> Mới</span>';
            }

            const prevRank = prevRankings.findIndex(s => s.userId === userId) + 1;
            const rankDiff = prevRank - currentRank;

            if (rankDiff > 0) {
                return `<span class="rank-change up"><i class="fas fa-arrow-up"></i> +${rankDiff}</span>`;
            } else if (rankDiff < 0) {
                return `<span class="rank-change down"><i class="fas fa-arrow-down"></i> ${rankDiff}</span>`;
            } else {
                return '<span class="rank-change"><i class="fas fa-minus"></i></span>';
            }
        }

        function updateTotalStudentsInfo() {
            // This function is now replaced by updateStatsBar
            updateStatsBar();
        }

        function getClassDisplayName(classId) {
            const classNames = {
                'python-a': 'Python - A',
                'python-b': 'Python - B',
                'python-c': 'Python - C'
            };
            return classNames[classId] || classId || 'Chưa phân lớp';
        }

        // View student profile (make it global)
        window.viewStudentProfile = function(userId) {
            // Redirect to student profile page with userId parameter
            window.location.href = `../auth/student-profile.html?userId=${userId}`;
        };

        // Load rankings when authenticated or allow guest access
        document.addEventListener('DOMContentLoaded', async function() {
            // Load badges first
            await loadBadges();

            onAuthStateChanged(auth, (user) => {
                if (user) {
                    console.log('User authenticated, loading rankings:', user.email);
                    loadRankings();
                } else {
                    console.log('No user authenticated, loading rankings as guest');
                    // For guest users, we'll need to handle this differently
                    // For now, show a message that login is required
                    loadRankings(); // Try anyway, might work with updated rules
                }
            });
        });
    </script>

    <script src="../assets/js/script.js"></script>
</body>
</html>