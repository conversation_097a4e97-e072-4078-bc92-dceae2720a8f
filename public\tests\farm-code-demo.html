<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Farm Code Challenge 2025 - Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2d5016 0%, #4a7c59 50%, #6b8e23 100%);
            color: white;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 3rem;
            background: linear-gradient(45deg, #90EE90, #32CD32, #228B22);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
            text-shadow: 0 0 30px rgba(144, 238, 144, 0.5);
        }

        .header p {
            font-size: 1.2rem;
            color: #b8e6b8;
        }

        .game-container {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 20px;
            margin-bottom: 30px;
        }

        .farm-grid {
            background: rgba(0, 0, 0, 0.4);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 2px;
            max-width: 400px;
            margin: 0 auto;
        }

        .cell {
            width: 40px;
            height: 40px;
            border: 1px solid #4a7c59;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .cell.soil {
            background: #8B4513;
        }

        .cell.planted {
            background: #654321;
        }

        .cell.growing {
            background: #228B22;
        }

        .cell.ready {
            background: #32CD32;
            animation: glow 2s infinite alternate;
        }

        .cell.drone {
            background: #FFD700;
            box-shadow: 0 0 15px #FFD700;
        }

        @keyframes glow {
            from { box-shadow: 0 0 5px #32CD32; }
            to { box-shadow: 0 0 20px #32CD32; }
        }

        .control-panel {
            background: rgba(0, 0, 0, 0.4);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }

        .stats {
            margin-bottom: 20px;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }

        .controls {
            margin-bottom: 20px;
        }

        .btn {
            width: 100%;
            padding: 10px;
            margin-bottom: 10px;
            border: none;
            border-radius: 8px;
            background: linear-gradient(45deg, #32CD32, #228B22);
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(50, 205, 50, 0.4);
        }

        .btn:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .code-builder {
            background: rgba(0, 0, 0, 0.4);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            margin-top: 20px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .code-builder h3 {
            margin-bottom: 15px;
            color: #90EE90;
            grid-column: 1 / -1;
        }

        .blocks-palette {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
        }

        .blocks-palette h4 {
            color: #FFD700;
            margin-bottom: 10px;
            font-size: 1rem;
        }

        .code-block {
            background: linear-gradient(45deg, #32CD32, #228B22);
            color: white;
            padding: 10px 15px;
            margin: 5px 0;
            border-radius: 8px;
            cursor: grab;
            user-select: none;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .code-block:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(50, 205, 50, 0.4);
        }

        .code-block.condition {
            background: linear-gradient(45deg, #FFD700, #FFA500);
            color: #000;
        }

        .code-block.action {
            background: linear-gradient(45deg, #32CD32, #228B22);
        }

        .code-block.loop {
            background: linear-gradient(45deg, #FF6B6B, #FF8E53);
        }

        .code-workspace {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            min-height: 400px;
            border: 2px dashed #4a7c59;
            position: relative;
        }

        .code-workspace h4 {
            color: #FFD700;
            margin-bottom: 15px;
            font-size: 1rem;
        }

        .dropped-block {
            background: rgba(50, 205, 50, 0.8);
            color: white;
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 6px;
            border-left: 4px solid #228B22;
            position: relative;
            cursor: pointer;
        }

        .dropped-block.condition {
            background: rgba(255, 215, 0, 0.8);
            color: #000;
            border-left-color: #FFA500;
        }

        .dropped-block.loop {
            background: rgba(255, 107, 107, 0.8);
            color: white;
            border-left-color: #FF6B6B;
        }

        .dropped-block .remove-btn {
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            background: #ff4444;
            color: white;
            border: none;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            cursor: pointer;
        }

        .generated-code {
            background: #1a1a1a;
            color: #90EE90;
            border: 1px solid #4a7c59;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin-top: 15px;
            max-height: 200px;
            overflow-y: auto;
            grid-column: 1 / -1;
        }

        .tutorial {
            background: rgba(255, 215, 0, 0.1);
            border: 2px solid #FFD700;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            color: #FFD700;
        }

        .tutorial h4 {
            margin-bottom: 10px;
            color: #FFD700;
        }

        .step {
            margin: 8px 0;
            padding-left: 20px;
            position: relative;
        }

        .step::before {
            content: "→";
            position: absolute;
            left: 0;
            color: #32CD32;
            font-weight: bold;
        }

        .weather {
            text-align: center;
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        .weather-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .leaderboard {
            background: rgba(0, 0, 0, 0.4);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            margin-top: 20px;
        }

        .leaderboard h3 {
            margin-bottom: 15px;
            color: #FFD700;
            text-align: center;
        }

        .leader-item {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            margin-bottom: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }

        .leader-item.top1 {
            background: linear-gradient(45deg, #FFD700, #FFA500);
            color: #000;
        }

        .leader-item.top2 {
            background: linear-gradient(45deg, #C0C0C0, #A0A0A0);
        }

        .leader-item.top3 {
            background: linear-gradient(45deg, #CD7F32, #B8860B);
        }

        @media (max-width: 768px) {
            .game-container {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .grid {
                grid-template-columns: repeat(6, 1fr);
            }
            
            .cell {
                width: 35px;
                height: 35px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌾 Farm Code Challenge 2025</h1>
            <p>Lập trình điều khiển drone nông nghiệp thông minh</p>
        </div>

        <div class="weather">
            <div class="weather-icon" id="weatherIcon">☀️</div>
            <div id="weatherText">Nắng - Cây trồng cần nhiều nước hơn</div>
        </div>

        <div class="game-container">
            <div class="farm-grid">
                <h3 style="text-align: center; margin-bottom: 15px; color: #90EE90;">🚁 Nông Trại Thông Minh</h3>
                <div class="grid" id="farmGrid"></div>
            </div>

            <div class="control-panel">
                <div class="stats">
                    <h3 style="color: #90EE90; margin-bottom: 15px;">📊 Thống Kê</h3>
                    <div class="stat-item">
                        <span>💰 Điểm số:</span>
                        <span id="score">0</span>
                    </div>
                    <div class="stat-item">
                        <span>🌱 Hạt giống:</span>
                        <span id="seeds">10</span>
                    </div>
                    <div class="stat-item">
                        <span>💧 Nước:</span>
                        <span id="water">20</span>
                    </div>
                    <div class="stat-item">
                        <span>🔋 Năng lượng:</span>
                        <span id="energy">100</span>
                    </div>
                    <div class="stat-item">
                        <span>🕐 Thời gian:</span>
                        <span id="time">0s</span>
                    </div>
                </div>

                <div class="controls">
                    <h3 style="color: #90EE90; margin-bottom: 15px;">🎮 Điều Khiển</h3>
                    <button class="btn" onclick="plantSeed()">🌱 Gieo Hạt</button>
                    <button class="btn" onclick="waterCrop()">💧 Tưới Nước</button>
                    <button class="btn" onclick="harvestCrop()">🌾 Thu Hoạch</button>
                    <button class="btn" onclick="moveDrone()">🚁 Di Chuyển</button>
                    <button class="btn" onclick="runAutoCode()" style="background: linear-gradient(45deg, #FFD700, #FFA500);">⚡ Chạy Code Tự Động</button>
                </div>
            </div>
        </div>

        <div class="tutorial">
            <h4>📚 Hướng dẫn sử dụng:</h4>
            <div class="step">Kéo các khối lệnh từ bên trái vào vùng làm việc bên phải</div>
            <div class="step">Sắp xếp theo thứ tự logic: Kiểm tra điều kiện → Thực hiện hành động</div>
            <div class="step">Nhấn "Chạy Code" để drone thực hiện theo thuật toán của bạn</div>
            <div class="step">Mục tiêu: Thu hoạch càng nhiều càng tốt với ít tài nguyên nhất!</div>
        </div>

        <div class="code-builder">
            <h3>🧩 Xây dựng thuật toán bằng kéo thả</h3>

            <div class="blocks-palette">
                <h4>🔍 Điều kiện (Conditions)</h4>
                <div class="code-block condition" draggable="true" data-code="if can_plant():" data-type="condition">
                    🌱 Nếu có thể gieo hạt
                </div>
                <div class="code-block condition" draggable="true" data-code="if can_water():" data-type="condition">
                    💧 Nếu có thể tưới nước
                </div>
                <div class="code-block condition" draggable="true" data-code="if can_harvest():" data-type="condition">
                    🌾 Nếu có thể thu hoạch
                </div>
                <div class="code-block condition" draggable="true" data-code="if energy > 50:" data-type="condition">
                    🔋 Nếu năng lượng > 50
                </div>

                <h4>⚡ Hành động (Actions)</h4>
                <div class="code-block action" draggable="true" data-code="    plant_seed()" data-type="action">
                    🌱 Gieo hạt
                </div>
                <div class="code-block action" draggable="true" data-code="    water_crop()" data-type="action">
                    💧 Tưới nước
                </div>
                <div class="code-block action" draggable="true" data-code="    harvest_crop()" data-type="action">
                    🌾 Thu hoạch
                </div>
                <div class="code-block action" draggable="true" data-code="    move_random()" data-type="action">
                    🚁 Di chuyển ngẫu nhiên
                </div>
                <div class="code-block action" draggable="true" data-code="    wait(1)" data-type="action">
                    ⏱️ Chờ 1 giây
                </div>

                <h4>🔄 Vòng lặp (Loops)</h4>
                <div class="code-block loop" draggable="true" data-code="for i in range(10):" data-type="loop">
                    🔄 Lặp 10 lần
                </div>
                <div class="code-block loop" draggable="true" data-code="while energy > 10:" data-type="loop">
                    🔄 Lặp khi năng lượng > 10
                </div>
            </div>

            <div class="code-workspace" id="codeWorkspace">
                <h4>🎯 Vùng làm việc - Kéo khối lệnh vào đây</h4>
                <div style="color: #888; font-style: italic; margin-top: 50px; text-align: center;">
                    Kéo các khối lệnh từ bên trái vào đây để xây dựng thuật toán
                </div>
            </div>

            <div class="generated-code" id="generatedCode">
# Code Python sẽ được tạo tự động từ các khối lệnh của bạn
def auto_farm():
    # Kéo thả các khối lệnh để tạo thuật toán
    pass
            </div>
        </div>

        <div class="leaderboard">
            <h3>🏆 Bảng Xếp Hạng</h3>
            <div class="leader-item top1">
                <span>🥇 CodeMaster2025</span>
                <span>2,450 điểm</span>
            </div>
            <div class="leader-item top2">
                <span>🥈 FarmBot_Pro</span>
                <span>2,180 điểm</span>
            </div>
            <div class="leader-item top3">
                <span>🥉 DroneExpert</span>
                <span>1,920 điểm</span>
            </div>
            <div class="leader-item">
                <span>4. AutoFarmer</span>
                <span>1,650 điểm</span>
            </div>
            <div class="leader-item">
                <span>5. SmartCrop</span>
                <span>1,420 điểm</span>
            </div>
        </div>
    </div>

    <script>
        // Game state
        let gameState = {
            droneX: 0,
            droneY: 0,
            score: 0,
            seeds: 10,
            water: 20,
            energy: 100,
            time: 0,
            grid: [],
            weather: 'sunny'
        };

        // Initialize game
        function initGame() {
            const grid = document.getElementById('farmGrid');
            gameState.grid = [];
            
            for (let i = 0; i < 64; i++) {
                const cell = document.createElement('div');
                cell.className = 'cell soil';
                cell.dataset.index = i;
                cell.onclick = () => selectCell(i);
                grid.appendChild(cell);
                
                gameState.grid.push({
                    type: 'soil',
                    growth: 0,
                    watered: false
                });
            }
            
            updateDronePosition();
            updateStats();
            startGameTimer();
            changeWeather();
        }

        function selectCell(index) {
            const x = index % 8;
            const y = Math.floor(index / 8);
            gameState.droneX = x;
            gameState.droneY = y;
            updateDronePosition();
        }

        function updateDronePosition() {
            const cells = document.querySelectorAll('.cell');
            cells.forEach(cell => cell.classList.remove('drone'));
            
            const droneIndex = gameState.droneY * 8 + gameState.droneX;
            cells[droneIndex].classList.add('drone');
        }

        function plantSeed() {
            if (gameState.seeds <= 0 || gameState.energy < 5) return;
            
            const index = gameState.droneY * 8 + gameState.droneX;
            const cell = gameState.grid[index];
            
            if (cell.type === 'soil') {
                cell.type = 'planted';
                cell.growth = 1;
                gameState.seeds--;
                gameState.energy -= 5;
                updateCellDisplay(index);
                updateStats();
            }
        }

        function waterCrop() {
            if (gameState.water <= 0 || gameState.energy < 3) return;
            
            const index = gameState.droneY * 8 + gameState.droneX;
            const cell = gameState.grid[index];
            
            if (cell.type === 'planted' || cell.type === 'growing') {
                cell.watered = true;
                cell.growth += gameState.weather === 'rain' ? 2 : 1;
                gameState.water--;
                gameState.energy -= 3;
                
                if (cell.growth >= 3) {
                    cell.type = 'ready';
                }
                
                updateCellDisplay(index);
                updateStats();
            }
        }

        function harvestCrop() {
            if (gameState.energy < 8) return;
            
            const index = gameState.droneY * 8 + gameState.droneX;
            const cell = gameState.grid[index];
            
            if (cell.type === 'ready') {
                const points = gameState.weather === 'sunny' ? 50 : 30;
                gameState.score += points;
                gameState.seeds += 2;
                gameState.energy -= 8;
                
                cell.type = 'soil';
                cell.growth = 0;
                cell.watered = false;
                
                updateCellDisplay(index);
                updateStats();
            }
        }

        function moveDrone() {
            if (gameState.energy < 2) return;
            
            // Random movement for demo
            const directions = [
                {x: 0, y: -1}, {x: 1, y: 0}, {x: 0, y: 1}, {x: -1, y: 0}
            ];
            const dir = directions[Math.floor(Math.random() * 4)];
            
            const newX = Math.max(0, Math.min(7, gameState.droneX + dir.x));
            const newY = Math.max(0, Math.min(7, gameState.droneY + dir.y));
            
            gameState.droneX = newX;
            gameState.droneY = newY;
            gameState.energy -= 2;
            
            updateDronePosition();
            updateStats();
        }

        function updateCellDisplay(index) {
            const cell = document.querySelector(`[data-index="${index}"]`);
            const cellData = gameState.grid[index];
            
            cell.className = 'cell';
            
            switch(cellData.type) {
                case 'soil':
                    cell.className += ' soil';
                    cell.textContent = '';
                    break;
                case 'planted':
                    cell.className += ' planted';
                    cell.textContent = '🌱';
                    break;
                case 'growing':
                    cell.className += ' growing';
                    cell.textContent = '🌿';
                    break;
                case 'ready':
                    cell.className += ' ready';
                    cell.textContent = '🌾';
                    break;
            }
            
            if (index === gameState.droneY * 8 + gameState.droneX) {
                cell.classList.add('drone');
            }
        }

        function updateStats() {
            document.getElementById('score').textContent = gameState.score;
            document.getElementById('seeds').textContent = gameState.seeds;
            document.getElementById('water').textContent = gameState.water;
            document.getElementById('energy').textContent = gameState.energy;
        }

        function startGameTimer() {
            setInterval(() => {
                gameState.time++;
                document.getElementById('time').textContent = gameState.time + 's';
                
                // Auto grow crops
                gameState.grid.forEach((cell, index) => {
                    if (cell.type === 'planted' && cell.watered && Math.random() < 0.1) {
                        cell.type = 'growing';
                        updateCellDisplay(index);
                    } else if (cell.type === 'growing' && Math.random() < 0.05) {
                        cell.type = 'ready';
                        updateCellDisplay(index);
                    }
                });
                
                // Regenerate resources slowly
                if (gameState.time % 10 === 0) {
                    gameState.energy = Math.min(100, gameState.energy + 5);
                    gameState.water = Math.min(50, gameState.water + 1);
                    updateStats();
                }
            }, 1000);
        }

        function changeWeather() {
            const weathers = [
                {type: 'sunny', icon: '☀️', text: 'Nắng - Cây trồng cần nhiều nước hơn'},
                {type: 'rain', icon: '🌧️', text: 'Mưa - Cây phát triển nhanh hơn'},
                {type: 'storm', icon: '⛈️', text: 'Bão - Nguy hiểm cho cây trồng!'}
            ];
            
            setInterval(() => {
                const weather = weathers[Math.floor(Math.random() * weathers.length)];
                gameState.weather = weather.type;
                document.getElementById('weatherIcon').textContent = weather.icon;
                document.getElementById('weatherText').textContent = weather.text;
            }, 15000);
        }

        // Drag and Drop functionality
        let droppedBlocks = [];

        function setupDragAndDrop() {
            const blocks = document.querySelectorAll('.code-block');
            const workspace = document.getElementById('codeWorkspace');

            blocks.forEach(block => {
                block.addEventListener('dragstart', (e) => {
                    e.dataTransfer.setData('text/plain', JSON.stringify({
                        code: block.dataset.code,
                        type: block.dataset.type,
                        text: block.textContent.trim()
                    }));
                });
            });

            workspace.addEventListener('dragover', (e) => {
                e.preventDefault();
                workspace.style.borderColor = '#32CD32';
                workspace.style.backgroundColor = 'rgba(50, 205, 50, 0.1)';
            });

            workspace.addEventListener('dragleave', (e) => {
                workspace.style.borderColor = '#4a7c59';
                workspace.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
            });

            workspace.addEventListener('drop', (e) => {
                e.preventDefault();
                workspace.style.borderColor = '#4a7c59';
                workspace.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';

                const data = JSON.parse(e.dataTransfer.getData('text/plain'));
                addBlockToWorkspace(data);
            });
        }

        function addBlockToWorkspace(blockData) {
            const workspace = document.getElementById('codeWorkspace');
            const blockElement = document.createElement('div');
            blockElement.className = `dropped-block ${blockData.type}`;
            blockElement.innerHTML = `
                ${blockData.text}
                <button class="remove-btn" onclick="removeBlock(this)">×</button>
            `;
            blockElement.dataset.code = blockData.code;
            blockElement.dataset.type = blockData.type;

            // Insert before the placeholder text or at the end
            const placeholder = workspace.querySelector('div[style*="color: #888"]');
            if (placeholder) {
                workspace.insertBefore(blockElement, placeholder);
                placeholder.style.display = 'none';
            } else {
                workspace.appendChild(blockElement);
            }

            droppedBlocks.push(blockData);
            generateCode();
        }

        function removeBlock(button) {
            const block = button.parentElement;
            const index = Array.from(block.parentElement.children).indexOf(block);
            droppedBlocks.splice(index, 1);
            block.remove();

            // Show placeholder if no blocks
            const workspace = document.getElementById('codeWorkspace');
            const placeholder = workspace.querySelector('div[style*="color: #888"]');
            if (droppedBlocks.length === 0 && placeholder) {
                placeholder.style.display = 'block';
            }

            generateCode();
        }

        function generateCode() {
            const codeDisplay = document.getElementById('generatedCode');
            let code = 'def auto_farm():\n';

            if (droppedBlocks.length === 0) {
                code += '    # Kéo thả các khối lệnh để tạo thuật toán\n    pass';
            } else {
                droppedBlocks.forEach(block => {
                    if (block.type === 'condition' || block.type === 'loop') {
                        code += '    ' + block.code + '\n';
                    } else {
                        code += '    ' + block.code + '\n';
                    }
                });
                code += '\n# Gọi hàm để chạy thuật toán\nauto_farm()';
            }

            codeDisplay.textContent = code;
        }

        function runAutoCode() {
            if (droppedBlocks.length === 0) {
                alert('⚠️ Hãy kéo thả các khối lệnh vào vùng làm việc trước khi chạy!');
                return;
            }

            alert('🚀 Đang chạy thuật toán của bạn...\n\n' +
                  'Drone sẽ thực hiện các lệnh theo thứ tự:\n' +
                  droppedBlocks.map((block, i) => `${i+1}. ${block.text}`).join('\n'));

            // Simulate auto execution
            executeBlocks();
        }

        function executeBlocks() {
            let currentBlock = 0;
            const executeNext = () => {
                if (currentBlock >= droppedBlocks.length) {
                    alert('✅ Hoàn thành thuật toán! Điểm số: ' + gameState.score);
                    return;
                }

                const block = droppedBlocks[currentBlock];

                // Highlight current executing block
                const workspaceBlocks = document.querySelectorAll('.dropped-block');
                workspaceBlocks.forEach(b => b.style.border = 'none');
                if (workspaceBlocks[currentBlock]) {
                    workspaceBlocks[currentBlock].style.border = '3px solid #FFD700';
                }

                // Execute based on block type
                switch(block.code) {
                    case 'if can_plant():':
                        if (gameState.seeds > 0 && gameState.energy >= 5) {
                            setTimeout(() => {
                                plantSeed();
                                currentBlock++;
                                executeNext();
                            }, 1000);
                        } else {
                            currentBlock++;
                            executeNext();
                        }
                        break;
                    case 'if can_water():':
                        if (gameState.water > 0 && gameState.energy >= 3) {
                            setTimeout(() => {
                                waterCrop();
                                currentBlock++;
                                executeNext();
                            }, 1000);
                        } else {
                            currentBlock++;
                            executeNext();
                        }
                        break;
                    case 'if can_harvest():':
                        const index = gameState.droneY * 8 + gameState.droneX;
                        if (gameState.grid[index].type === 'ready') {
                            setTimeout(() => {
                                harvestCrop();
                                currentBlock++;
                                executeNext();
                            }, 1000);
                        } else {
                            currentBlock++;
                            executeNext();
                        }
                        break;
                    case '    plant_seed()':
                        setTimeout(() => {
                            plantSeed();
                            currentBlock++;
                            executeNext();
                        }, 1000);
                        break;
                    case '    water_crop()':
                        setTimeout(() => {
                            waterCrop();
                            currentBlock++;
                            executeNext();
                        }, 1000);
                        break;
                    case '    harvest_crop()':
                        setTimeout(() => {
                            harvestCrop();
                            currentBlock++;
                            executeNext();
                        }, 1000);
                        break;
                    case '    move_random()':
                        setTimeout(() => {
                            moveDrone();
                            currentBlock++;
                            executeNext();
                        }, 1000);
                        break;
                    default:
                        currentBlock++;
                        executeNext();
                }
            };

            executeNext();
        }

        // Initialize game when page loads
        window.onload = () => {
            initGame();
            setupDragAndDrop();
        };
    </script>
</body>
</html>
