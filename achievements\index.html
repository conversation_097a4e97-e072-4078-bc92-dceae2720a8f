<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Th<PERSON>nh <PERSON> - Vthon</title>
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="../assets/images/favicon.png">
    <link rel="shortcut icon" type="image/png" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="72x72" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="114x114" href="../assets/images/favicon.png">

    <link rel="stylesheet" href="../assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        /* Dark theme background */
        body {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            min-height: 100vh;
            color: white;
        }

        .page-header {
            text-align: center;
            margin: 120px 0 100px;
            position: relative;
            z-index: 10;
        }

        .page-header h1 {
            font-size: 3rem;
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
            font-weight: 700;
        }

        .page-header p {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.3rem;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
        }

        /* Timeline Container */
        .timeline-container {
            position: relative;
            max-width: 1200px;
            margin: 0 auto;
            padding: 50px 20px;
        }

        /* Central Timeline Line */
        .timeline-line {
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(to bottom, #ffd700, #ffed4e, #ffd700);
            transform: translateX(-50%);
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
        }

        /* Timeline Items */
        .timeline-item {
            position: relative;
            margin-bottom: 100px;
            opacity: 0;
            transform: translateY(50px);
            transition: all 0.8s ease;
        }

        .timeline-item.animate {
            opacity: 1;
            transform: translateY(0);
        }

        .timeline-item:nth-child(odd) .timeline-content {
            margin-left: 0;
            margin-right: 60%;
            text-align: right;
        }

        .timeline-item:nth-child(even) .timeline-content {
            margin-left: 60%;
            margin-right: 0;
            text-align: left;
        }

        /* Timeline Year Marker */
        .timeline-year {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #1a1a2e;
            padding: 15px 25px;
            border-radius: 50px;
            font-weight: 700;
            font-size: 1.2rem;
            box-shadow: 0 5px 20px rgba(255, 215, 0, 0.3);
            z-index: 10;
        }

        /* Timeline Content */
        .timeline-content {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 30px;
            position: relative;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .timeline-content::before {
            content: '';
            position: absolute;
            top: 50%;
            width: 0;
            height: 0;
            border: 15px solid transparent;
        }

        .timeline-item:nth-child(odd) .timeline-content::before {
            right: -30px;
            border-left-color: rgba(255, 255, 255, 0.1);
            transform: translateY(-50%);
        }

        .timeline-item:nth-child(even) .timeline-content::before {
            left: -30px;
            border-right-color: rgba(255, 255, 255, 0.1);
            transform: translateY(-50%);
        }

        /* Achievement Title */
        .achievement-title {
            font-size: 1.8rem;
            color: #ffd700;
            margin-bottom: 15px;
            font-weight: 700;
            line-height: 1.3;
        }

        .achievement-description {
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 25px;
            line-height: 1.6;
            font-size: 1.1rem;
        }

        /* Achievement Badges */
        .achievement-badges {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 20px;
        }

        .achievement-badge {
            position: relative;
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
        }

        .achievement-badge:hover {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 10px 25px rgba(255, 215, 0, 0.5);
        }

        .achievement-badge i {
            font-size: 2rem;
            color: #1a1a2e;
        }

        /* Tooltip for badges */
        .badge-tooltip {
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 10px 15px;
            border-radius: 8px;
            font-size: 0.9rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 100;
            margin-bottom: 10px;
        }

        .badge-tooltip::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 5px solid transparent;
            border-top-color: rgba(0, 0, 0, 0.9);
        }

        .achievement-badge:hover .badge-tooltip {
            opacity: 1;
            visibility: visible;
        }

        /* Scroll Progress Indicator */
        .scroll-progress {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.1);
            z-index: 1000;
        }

        .scroll-progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #ffd700, #ffed4e);
            width: 0%;
            transition: width 0.1s ease;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .timeline-line {
                left: 30px;
            }

            .timeline-item:nth-child(odd) .timeline-content,
            .timeline-item:nth-child(even) .timeline-content {
                margin-left: 80px;
                margin-right: 0;
                text-align: left;
            }

            .timeline-item:nth-child(odd) .timeline-content::before,
            .timeline-item:nth-child(even) .timeline-content::before {
                left: -30px;
                right: auto;
                border-right-color: rgba(255, 255, 255, 0.1);
                border-left-color: transparent;
            }

            .timeline-year {
                left: 30px;
                transform: translateX(-50%);
            }

            .achievement-badges {
                justify-content: center;
            }

            .page-header h1 {
                font-size: 2.2rem;
            }
        }

        /* Loading and animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .timeline-year {
            animation: pulse 2s infinite;
        }

        /* Floating particles effect */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #ffd700;
            border-radius: 50%;
            opacity: 0.7;
            animation: float 6s infinite linear;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 0.7;
            }
            90% {
                opacity: 0.7;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        /* Enhanced hover effects */
        .timeline-content:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(255, 215, 0, 0.2);
        }

        /* Achievement Image Styles */
        .achievement-image {
            margin-top: 20px;
            text-align: center;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .achievement-image img {
            width: 100%;
            max-width: 400px;
            height: auto;
            border-radius: 15px;
            transition: transform 0.3s ease, filter 0.3s ease;
            cursor: pointer;
        }

        .achievement-image img:hover {
            transform: scale(1.05);
            filter: brightness(1.1);
        }

        /* Tooltip Styles */
        .custom-tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: #ffd700;
            padding: 10px 15px;
            border-radius: 8px;
            font-size: 0.9rem;
            max-width: 300px;
            z-index: 1000;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
            border: 1px solid rgba(255, 215, 0, 0.3);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
        }

        .custom-tooltip.show {
            opacity: 1;
        }

        /* Glowing effect for timeline line */
        .timeline-line::before {
            content: '';
            position: absolute;
            left: -2px;
            top: 0;
            bottom: 0;
            width: 8px;
            background: linear-gradient(to bottom, transparent, rgba(255, 215, 0, 0.3), transparent);
            border-radius: 4px;
            animation: glow 3s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from {
                opacity: 0.5;
                transform: scaleX(1);
            }
            to {
                opacity: 1;
                transform: scaleX(1.2);
            }
        }

    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../index.html">Trang Chủ</a></li>
                    <li><a href="../classes/">Lớp Học</a></li>
                    <li><a href="index.html" class="active">Thành Tích</a></li>
                    <li><a href="../auth/register.html">Đăng Ký</a></li>
                    <li><a href="../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../research/">Sự kiện</a></li>
                    <li><a href="../auth/">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Scroll Progress -->
    <div class="scroll-progress">
        <div class="scroll-progress-bar" id="scrollProgressBar"></div>
    </div>

    <!-- Floating Particles -->
    <div class="particles" id="particles"></div>

    <!-- Achievements Section -->
    <section>
        <div class="container">
            <div class="page-header">
                <h1>Thành Tích Vthon Academy</h1>
                <p>Hành trình phát triển và những thành tựu đáng tự hào của Vthon Academy cùng học viên</p>
            </div>

            <div class="timeline-container">
                <div class="timeline-line"></div>

                <!-- 2022 -->
                <div class="timeline-item">
                    <div class="timeline-year">2022</div>
                    <div class="timeline-content">
                        <h3 class="achievement-title">CouNa - Hệ thống hỗ trợ bệnh nhân Covid-19</h3>
                        <p class="achievement-description">
                            <strong>Giải thưởng:</strong> Giải nhất cấp tỉnh cuộc thi Nghiên cứu KHKT. Được dự thi cấp quốc gia.<br>
                            <strong>Tác giả:</strong> Lê Quang Vinh<br>
                            Dự án phát triển hệ thống hỗ trợ toàn diện cho bệnh nhân Covid-19, từ theo dõi sức khỏe đến kết nối y tế.
                        </p>
                        <div class="achievement-badges">
                            <div class="achievement-badge">
                                <i class="fas fa-trophy"></i>
                                <div class="badge-tooltip">Giải Nhất Cấp Tỉnh KHKT</div>
                            </div>
                            <div class="achievement-badge">
                                <i class="fas fa-medal"></i>
                                <div class="badge-tooltip">Tham Dự KHKT Quốc Gia</div>
                            </div>
                            <div class="achievement-badge">
                                <i class="fas fa-laptop-medical"></i>
                                <div class="badge-tooltip">Embedded Systems - Y tế</div>
                            </div>
                        </div>
                        <div class="achievement-image">
                            <img src="../assets/images/achievements/covid-app-award.jpg" alt="Chứng nhận giải thưởng CouNa 2022"
                                 onmouseover="showTooltip(this, 'Dự án CouNa - Hệ thống hỗ trợ bệnh nhân Covid-19, Giải nhất cấp tỉnh KHKT 2022')"
                                 onmouseout="hideTooltip()">
                        </div>
                    </div>
                </div>

                <!-- 2023 -->
                <div class="timeline-item">
                    <div class="timeline-year">2023</div>
                    <div class="timeline-content">
                        <h3 class="achievement-title">Ra Mắt Vthon Academy</h3>
                        <p class="achievement-description">
                            Vthon Academy chính thức mở cửa với lớp học đầu tiên. Khởi đầu cho sứ mệnh
                            đào tạo thế hệ lập trình viên trẻ tài năng trong lĩnh vực Python và AI.
                        </p>
                        <div class="achievement-badges">
                            <div class="achievement-badge">
                                <i class="fas fa-graduation-cap"></i>
                                <div class="badge-tooltip">Lớp Học Đầu Tiên</div>
                            </div>
                            <div class="achievement-badge">
                                <i class="fas fa-rocket"></i>
                                <div class="badge-tooltip">Khởi Động Academy</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 2024 -->
                <div class="timeline-item">
                    <div class="timeline-year">2024</div>
                    <div class="timeline-content">
                        <h3 class="achievement-title">Ứng dụng mô hình BlenderBot để xây dựng Chatbot học tập ngôn ngữ</h3>
                        <p class="achievement-description">
                            <strong>Giải thưởng:</strong> Giải tư cuộc thi KHKT Cấp Trường<br>
                            <strong>Tác giả:</strong> Nguyễn Trần Khả Nguyên<br>
                            Ứng dụng công nghệ AI tiên tiến để phát triển chatbot hỗ trợ học tập ngôn ngữ hiệu quả.
                        </p>
                        <div class="achievement-badges">
                            <div class="achievement-badge">
                                <i class="fas fa-robot"></i>
                                <div class="badge-tooltip">BlenderBot - AI Chatbot</div>
                            </div>
                            <div class="achievement-badge">
                                <i class="fas fa-award"></i>
                                <div class="badge-tooltip">Giải Tư Cấp Trường</div>
                            </div>
                            <div class="achievement-badge">
                                <i class="fas fa-language"></i>
                                <div class="badge-tooltip">Học tập ngôn ngữ</div>
                            </div>
                        </div>
                        <div class="achievement-image">
                            <img src="../assets/images/achievements/chatbot-project.jpg" alt="Dự án BlenderBot Chatbot 2024"
                                 onmouseover="showTooltip(this, 'Ứng dụng mô hình BlenderBot để xây dựng Chatbot học tập ngôn ngữ - Giải tư cấp trường 2024')"
                                 onmouseout="hideTooltip()">
                        </div>
                    </div>
                </div>

                <!-- 2024 - Project 2 -->
                <div class="timeline-item">
                    <div class="timeline-year">2024</div>
                    <div class="timeline-content">
                        <h3 class="achievement-title">Phần mềm chấm điểm trắc nghiệm</h3>
                        <p class="achievement-description">
                            <strong>Giải thưởng:</strong> Giải tư cuộc thi KHKT Cấp Trường<br>
                            <strong>Tác giả:</strong> Văn Minh Hiếu<br>
                            Phần mềm tự động hóa quá trình chấm điểm trắc nghiệm, nâng cao hiệu quả giáo dục.
                        </p>
                        <div class="achievement-badges">
                            <div class="achievement-badge">
                                <i class="fas fa-check-circle"></i>
                                <div class="badge-tooltip">Phần mềm chấm điểm</div>
                            </div>
                            <div class="achievement-badge">
                                <i class="fas fa-award"></i>
                                <div class="badge-tooltip">Giải Tư Cấp Trường</div>
                            </div>
                            <div class="achievement-badge">
                                <i class="fas fa-graduation-cap"></i>
                                <div class="badge-tooltip">Công nghệ giáo dục</div>
                            </div>
                        </div>
                        <div class="achievement-image">
                            <img src="../assets/images/achievements/grading-software.jpg" alt="Phần mềm chấm điểm trắc nghiệm 2024"
                                 onmouseover="showTooltip(this, 'Phần mềm chấm điểm trắc nghiệm - Giải tư cấp trường 2024')"
                                 onmouseout="hideTooltip()">
                        </div>
                    </div>
                </div>

                <!-- 2025 - Project 1 -->
                <div class="timeline-item">
                    <div class="timeline-year">2025</div>
                    <div class="timeline-content">
                        <h3 class="achievement-title">Hệ thống phát hiện sớm cháy rừng</h3>
                        <p class="achievement-description">
                            <strong>Giải thưởng:</strong> Giải ba cuộc thi KHKT Cấp Tỉnh<br>
                            <strong>Tác giả:</strong> Phan Bảo Khánh<br>
                            Hệ thống phát hiện sớm cháy rừng giúp ngăn chặn thiệt hại lớn cho môi trường.
                        </p>
                        <div class="achievement-badges">
                            <div class="achievement-badge">
                                <i class="fas fa-fire"></i>
                                <div class="badge-tooltip">Phát hiện cháy rừng</div>
                            </div>
                            <div class="achievement-badge">
                                <i class="fas fa-trophy"></i>
                                <div class="badge-tooltip">Giải Ba Cấp Tỉnh</div>
                            </div>
                            <div class="achievement-badge">
                                <i class="fas fa-leaf"></i>
                                <div class="badge-tooltip">Bảo vệ môi trường</div>
                            </div>
                        </div>
                        <div class="achievement-image">
                            <img src="../assets/images/achievements/forest-fire-detection-1.jpg" alt="Hệ thống phát hiện cháy rừng 2025"
                                 onmouseover="showTooltip(this, 'Hệ thống phát hiện sớm cháy rừng - Giải ba cấp tỉnh 2025')"
                                 onmouseout="hideTooltip()">
                        </div>
                    </div>
                </div>

                <!-- 2025 - Project 2 -->
                <div class="timeline-item">
                    <div class="timeline-year">2025</div>
                    <div class="timeline-content">
                        <h3 class="achievement-title">Hệ thống hỗ trợ dự đoán bệnh tim</h3>
                        <p class="achievement-description">
                            <strong>Giải thưởng:</strong> Giải nhì cuộc thi KHKT Cấp Tỉnh<br>
                            <strong>Tác giả:</strong> Nguyễn Minh Tiến, Quách Tiến Đạt<br>
                            Hệ thống hỗ trợ dự đoán bệnh tim thông qua điện tâm đồ và mô hình ngôn ngữ lớn.
                        </p>
                        <div class="achievement-badges">
                            <div class="achievement-badge">
                                <i class="fas fa-heartbeat"></i>
                                <div class="badge-tooltip">Dự đoán bệnh tim</div>
                            </div>
                            <div class="achievement-badge">
                                <i class="fas fa-trophy"></i>
                                <div class="badge-tooltip">Giải Nhì Cấp Tỉnh</div>
                            </div>
                            <div class="achievement-badge">
                                <i class="fas fa-brain"></i>
                                <div class="badge-tooltip">AI & Machine Learning</div>
                            </div>
                        </div>
                        <div class="achievement-image">
                            <img src="../assets/images/achievements/heart-disease-prediction-1.jpg" alt="Hệ thống dự đoán bệnh tim 2025"
                                 onmouseover="showTooltip(this, 'Hệ thống hỗ trợ dự đoán bệnh tim - Giải nhì cấp tỉnh 2025')"
                                 onmouseout="hideTooltip()">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script src="../assets/js/script.js"></script>
    <script>
        // Scroll-driven animation
        function initScrollAnimations() {
            const timelineItems = document.querySelectorAll('.timeline-item');
            const scrollProgressBar = document.getElementById('scrollProgressBar');

            // Intersection Observer for timeline items
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate');
                    }
                });
            }, {
                threshold: 0.3,
                rootMargin: '0px 0px -100px 0px'
            });

            timelineItems.forEach(item => {
                observer.observe(item);
            });

            // Scroll progress bar
            function updateScrollProgress() {
                const scrollTop = window.pageYOffset;
                const docHeight = document.documentElement.scrollHeight - window.innerHeight;
                const scrollPercent = (scrollTop / docHeight) * 100;
                scrollProgressBar.style.width = scrollPercent + '%';
            }

            window.addEventListener('scroll', updateScrollProgress);
            updateScrollProgress();
        }

        // Floating particles effect
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 15;

            function createParticle() {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 4) + 's';
                particlesContainer.appendChild(particle);

                // Remove particle after animation
                setTimeout(() => {
                    if (particle.parentNode) {
                        particle.parentNode.removeChild(particle);
                    }
                }, 8000);
            }

            // Create particles periodically
            setInterval(createParticle, 800);
        }

        // Tooltip functions
        let tooltip = null;

        function showTooltip(element, text) {
            // Remove existing tooltip
            hideTooltip();

            // Create new tooltip
            tooltip = document.createElement('div');
            tooltip.className = 'custom-tooltip';
            tooltip.textContent = text;
            document.body.appendChild(tooltip);

            // Position tooltip
            const rect = element.getBoundingClientRect();
            const tooltipRect = tooltip.getBoundingClientRect();

            let left = rect.left + (rect.width / 2) - (tooltipRect.width / 2);
            let top = rect.top - tooltipRect.height - 10;

            // Adjust if tooltip goes off screen
            if (left < 10) left = 10;
            if (left + tooltipRect.width > window.innerWidth - 10) {
                left = window.innerWidth - tooltipRect.width - 10;
            }
            if (top < 10) {
                top = rect.bottom + 10;
            }

            tooltip.style.left = left + window.scrollX + 'px';
            tooltip.style.top = top + window.scrollY + 'px';

            // Show tooltip with animation
            setTimeout(() => {
                if (tooltip) {
                    tooltip.classList.add('show');
                }
            }, 10);
        }

        function hideTooltip() {
            if (tooltip) {
                tooltip.remove();
                tooltip = null;
            }
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            initScrollAnimations();
            createParticles();
        });

        console.log('Achievements timeline loaded successfully');
    </script>
</body>
</html>
